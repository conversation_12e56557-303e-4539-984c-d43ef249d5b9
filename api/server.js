// API Server for Khobra Kitchens - SQLite Backend
// خادم API لخبرة المطابخ - قاعدة بيانات SQLite

const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const multer = require('multer');
const fs = require('fs');
const { exec } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3003;

// Database connection
const dbPath = path.join(__dirname, '../database/khobra_kitchens.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
  } else {
    console.log('✅ Connected to SQLite database');
  }
});

// Middleware
app.use(cors());
// زيادة حد حجم البيانات المرسلة لدعم رفع الصور
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// إعداد multer لرفع الصور
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '..', 'public', 'uploads', req.params.type || 'temp');
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // إنشاء اسم ملف فريد
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB حد أقصى لحجم الملف
  },
  fileFilter: function (req, file, cb) {
    // قبول الصور فقط
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)!'), false);
    }
  }
});

// الصور الآن في public/uploads وتُخدم مباشرة من الخادم الويب

// Function to sync uploads from public to dist
const syncUploads = () => {
  const syncScript = path.join(__dirname, '..', 'scripts', 'sync-uploads.sh');
  exec(`bash ${syncScript}`, (error, stdout, stderr) => {
    if (error) {
      console.error('Error syncing uploads:', error);
    } else {
      console.log('Uploads synced successfully');
    }
  });
};

// Helper function to promisify database operations
const dbGet = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
};

const dbAll = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
};

const dbRun = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve({ id: this.lastID, changes: this.changes });
    });
  });
};

// ==================== FILE UPLOAD API ====================

// رفع صورة واحدة
app.post('/api/upload/:type', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'لم يتم رفع أي ملف' });
    }

    // التحقق من حجم الملف
    if (req.file.size > 2 * 1024 * 1024) {
      return res.status(400).json({ error: 'حجم الملف يجب أن يكون أقل من 2 ميجابايت' });
    }

    // إرجاع مسار الصورة المرفوعة
    const imagePath = `/uploads/${req.params.type}/${req.file.filename}`;

    // Sync uploads to dist directory
    syncUploads();

    res.json({
      success: true,
      imagePath: imagePath,
      originalName: req.file.originalname,
      size: req.file.size,
      sizeFormatted: `${(req.file.size / 1024 / 1024).toFixed(2)} MB`
    });
  } catch (error) {
    console.error('Error uploading file:', error);

    // معالجة أخطاء multer المحددة
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت' });
    }
    if (error.message.includes('يُسمح برفع الصور فقط')) {
      return res.status(400).json({ error: error.message });
    }

    res.status(500).json({ error: 'فشل في رفع الملف' });
  }
});

// رفع عدة صور
app.post('/api/upload/:type/multiple', upload.array('images', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'لم يتم رفع أي ملفات' });
    }

    // إرجاع مسارات الصور المرفوعة
    const imagePaths = req.files.map(file => ({
      imagePath: `/uploads/${req.params.type}/${file.filename}`,
      originalName: file.originalname,
      size: file.size
    }));

    // Sync uploads to dist directory
    syncUploads();

    res.json({
      success: true,
      images: imagePaths
    });
  } catch (error) {
    console.error('Error uploading files:', error);
    res.status(500).json({ error: 'فشل في رفع الملفات' });
  }
});

// حذف صورة مرفوعة
app.delete('/api/upload', (req, res) => {
  try {
    const { imagePath } = req.body;
    if (!imagePath) {
      return res.status(400).json({ error: 'مسار الصورة مطلوب' });
    }

    // التأكد من أن المسار يبدأ بـ /uploads
    if (!imagePath.startsWith('/uploads/')) {
      return res.status(400).json({ error: 'مسار صورة غير صحيح' });
    }

    const fullPath = path.join(__dirname, '..', 'public', imagePath);

    // التحقق من وجود الملف
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      res.json({ success: true, message: 'تم حذف الصورة بنجاح' });
    } else {
      res.status(404).json({ error: 'الصورة غير موجودة' });
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({ error: 'فشل في حذف الصورة' });
  }
});

// ==================== DATABASE CLEANUP API ====================

// تنظيف روابط الصور من localhost
app.post('/api/cleanup-image-urls', async (req, res) => {
  try {
    // تحديث صور المطابخ
    const kitchens = await dbAll('SELECT * FROM kitchen_images');
    for (const image of kitchens) {
      if (image.image_url && image.image_url.includes('localhost:3002')) {
        const cleanUrl = image.image_url.replace('http://localhost:3002', '');
        await dbRun(
          'UPDATE kitchen_images SET image_url = ? WHERE id = ?',
          [cleanUrl, image.id]
        );
      }
    }

    // تحديث صور الخزائن
    const cabinets = await dbAll('SELECT * FROM cabinet_images');
    for (const image of cabinets) {
      if (image.image_url && image.image_url.includes('localhost:3002')) {
        const cleanUrl = image.image_url.replace('http://localhost:3002', '');
        await dbRun(
          'UPDATE cabinet_images SET image_url = ? WHERE id = ?',
          [cleanUrl, image.id]
        );
      }
    }

    res.json({
      success: true,
      message: 'تم تنظيف روابط الصور بنجاح',
      kitchens_updated: kitchens.length,
      cabinets_updated: cabinets.length
    });
  } catch (error) {
    console.error('Error cleaning up image URLs:', error);
    res.status(500).json({ error: 'فشل في تنظيف روابط الصور' });
  }
});

// ==================== HERO SECTION API ====================

// Get hero data
app.get('/api/hero', async (req, res) => {
  try {
    const hero = await dbGet('SELECT * FROM hero_section WHERE is_active = 1 ORDER BY id DESC LIMIT 1');
    res.json(hero || {});
  } catch (error) {
    console.error('Error fetching hero data:', error);
    res.status(500).json({ error: 'Failed to fetch hero data' });
  }
});

// Get last update time for hero data
app.get('/api/hero/last-update', async (req, res) => {
  try {
    const result = await dbGet('SELECT updated_at FROM hero_section WHERE is_active = 1 ORDER BY updated_at DESC LIMIT 1');
    res.json({ last_update: result?.updated_at || null });
  } catch (error) {
    console.error('Error fetching hero last update:', error);
    res.status(500).json({ error: 'Failed to fetch last update time' });
  }
});

// Update hero data
app.put('/api/hero/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, subtitle, background_image, primary_button_text, secondary_button_text } = req.body;

    await dbRun(
      'UPDATE hero_section SET title = ?, subtitle = ?, background_image = ?, primary_button_text = ?, secondary_button_text = ?, updated_at = datetime("now") WHERE id = ?',
      [title, subtitle, background_image, primary_button_text, secondary_button_text, id]
    );

    console.log('✅ Hero data updated successfully');
    res.json({ success: true, updated_at: new Date().toISOString() });
  } catch (error) {
    console.error('Error updating hero data:', error);
    res.status(500).json({ error: 'Failed to update hero data' });
  }
});

// ==================== KITCHENS API ====================

// Get all kitchens
app.get('/api/kitchens', async (req, res) => {
  try {
    const kitchens = await dbAll(`
      SELECT k.*, c.name as category_name, c.slug as category_slug
      FROM kitchens k
      LEFT JOIN categories c ON k.category_id = c.id
      WHERE k.is_active = 1
      ORDER BY k.sort_order, k.id
    `);

    // Get images for each kitchen
    for (const kitchen of kitchens) {
      kitchen.images = await dbAll(
        'SELECT * FROM kitchen_images WHERE kitchen_id = ? ORDER BY sort_order',
        [kitchen.id]
      );
    }

    res.json(kitchens);
  } catch (error) {
    console.error('Error fetching kitchens:', error);
    res.status(500).json({ error: 'Failed to fetch kitchens' });
  }
});

// Add new kitchen
app.post('/api/kitchens', async (req, res) => {
  try {
    const { title, description, category_id, is_featured, sort_order, images } = req.body;

    // إنشاء عنوان تلقائي بناءً على الفئة إذا لم يتم توفيره
    let finalTitle = (title && title.trim() !== '') ? title.trim() : 'مطبخ جديد';

    // إذا لم يتم توفير عنوان، احصل على اسم الفئة لإنشاء عنوان أفضل
    if (!title || title.trim() === '') {
      if (category_id) {
        const category = await dbGet('SELECT name FROM categories WHERE id = ?', [category_id]);
        if (category) {
          // احصل على عدد المطابخ الموجودة في نفس الفئة لإنشاء عنوان فريد
          const count = await dbGet('SELECT COUNT(*) as count FROM kitchens WHERE category_id = ?', [category_id]);
          const kitchenNumber = count.count + 1;
          finalTitle = category.name.replace('مطابخ', 'مطبخ') + (kitchenNumber > 1 ? ` ${kitchenNumber}` : '');
        }
      }
    }

    const finalDescription = (description && description.trim() !== '') ? description.trim() : '';

    const result = await dbRun(
      'INSERT INTO kitchens (title, description, category_id, is_featured, sort_order) VALUES (?, ?, ?, ?, ?)',
      [finalTitle, finalDescription, category_id, is_featured ? 1 : 0, sort_order || 0]
    );

    const kitchenId = result.id;

    // Add images
    if (images && Array.isArray(images)) {
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        await dbRun(
          'INSERT INTO kitchen_images (kitchen_id, image_url, alt_text, sort_order, is_primary) VALUES (?, ?, ?, ?, ?)',
          [kitchenId, image.image_url || image, image.alt_text || finalTitle, i + 1, i === 0 ? 1 : 0]
        );
      }
    }

    res.json({ id: kitchenId, success: true });
  } catch (error) {
    console.error('Error adding kitchen:', error);
    res.status(500).json({ error: 'Failed to add kitchen' });
  }
});

// Update kitchen
app.put('/api/kitchens/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, category_id, is_featured, sort_order, images } = req.body;

    // إنشاء عنوان تلقائي إذا لم يتم توفيره
    const finalTitle = (title && title.trim() !== '') ? title.trim() : 'مطبخ محدث';
    const finalDescription = (description && description.trim() !== '') ? description.trim() : '';

    await dbRun(
      'UPDATE kitchens SET title = ?, description = ?, category_id = ?, is_featured = ?, sort_order = ?, updated_at = datetime("now") WHERE id = ?',
      [finalTitle, finalDescription, category_id, is_featured ? 1 : 0, sort_order, id]
    );

    // Update images if provided
    if (images && Array.isArray(images)) {
      // Delete existing images
      await dbRun('DELETE FROM kitchen_images WHERE kitchen_id = ?', [id]);
      
      // Insert new images
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        await dbRun(
          'INSERT INTO kitchen_images (kitchen_id, image_url, alt_text, sort_order, is_primary) VALUES (?, ?, ?, ?, ?)',
          [id, image.image_url || image, image.alt_text || finalTitle, i + 1, i === 0 ? 1 : 0]
        );
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating kitchen:', error);
    res.status(500).json({ error: 'Failed to update kitchen' });
  }
});

// Delete kitchen
app.delete('/api/kitchens/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Delete kitchen (images will be deleted automatically due to CASCADE)
    await dbRun('DELETE FROM kitchens WHERE id = ?', [id]);
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting kitchen:', error);
    res.status(500).json({ error: 'Failed to delete kitchen' });
  }
});

// ==================== CABINETS API ====================

// Get all cabinets
app.get('/api/cabinets', async (req, res) => {
  try {
    const cabinets = await dbAll(`
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM cabinets c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_active = 1
      ORDER BY c.sort_order, c.id
    `);

    // Get images for each cabinet
    for (const cabinet of cabinets) {
      cabinet.images = await dbAll(
        'SELECT * FROM cabinet_images WHERE cabinet_id = ? ORDER BY sort_order',
        [cabinet.id]
      );
    }

    res.json(cabinets);
  } catch (error) {
    console.error('Error fetching cabinets:', error);
    res.status(500).json({ error: 'Failed to fetch cabinets' });
  }
});

// Add new cabinet
app.post('/api/cabinets', async (req, res) => {
  try {
    const { title, description, category_id, is_featured, sort_order, images } = req.body;

    // إنشاء عنوان تلقائي بناءً على الفئة إذا لم يتم توفيره
    let finalTitle = (title && title.trim() !== '') ? title.trim() : 'خزانة جديدة';

    // إذا لم يتم توفير عنوان، احصل على اسم الفئة لإنشاء عنوان أفضل
    if (!title || title.trim() === '') {
      if (category_id) {
        const category = await dbGet('SELECT name FROM categories WHERE id = ?', [category_id]);
        if (category) {
          // احصل على عدد الخزائن الموجودة في نفس الفئة لإنشاء عنوان فريد
          const count = await dbGet('SELECT COUNT(*) as count FROM cabinets WHERE category_id = ?', [category_id]);
          const cabinetNumber = count.count + 1;
          finalTitle = category.name.replace('خزائن', 'خزانة') + (cabinetNumber > 1 ? ` ${cabinetNumber}` : '');
        }
      }
    }

    const finalDescription = (description && description.trim() !== '') ? description.trim() : '';

    const result = await dbRun(
      'INSERT INTO cabinets (title, description, category_id, is_featured, sort_order) VALUES (?, ?, ?, ?, ?)',
      [finalTitle, finalDescription, category_id, is_featured ? 1 : 0, sort_order || 0]
    );

    const cabinetId = result.id;

    // Add images
    if (images && Array.isArray(images)) {
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        await dbRun(
          'INSERT INTO cabinet_images (cabinet_id, image_url, alt_text, sort_order, is_primary) VALUES (?, ?, ?, ?, ?)',
          [cabinetId, image.image_url || image, image.alt_text || finalTitle, i + 1, i === 0 ? 1 : 0]
        );
      }
    }

    res.json({ id: cabinetId, success: true });
  } catch (error) {
    console.error('Error adding cabinet:', error);
    res.status(500).json({ error: 'Failed to add cabinet' });
  }
});

// Update cabinet
app.put('/api/cabinets/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, category_id, is_featured, sort_order, images } = req.body;

    // إنشاء عنوان تلقائي إذا لم يتم توفيره
    const finalTitle = (title && title.trim() !== '') ? title.trim() : 'خزانة محدثة';
    const finalDescription = (description && description.trim() !== '') ? description.trim() : '';

    await dbRun(
      'UPDATE cabinets SET title = ?, description = ?, category_id = ?, is_featured = ?, sort_order = ?, updated_at = datetime("now") WHERE id = ?',
      [finalTitle, finalDescription, category_id, is_featured ? 1 : 0, sort_order, id]
    );

    // Update images if provided
    if (images && Array.isArray(images)) {
      // Delete existing images
      await dbRun('DELETE FROM cabinet_images WHERE cabinet_id = ?', [id]);
      
      // Insert new images
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        await dbRun(
          'INSERT INTO cabinet_images (cabinet_id, image_url, alt_text, sort_order, is_primary) VALUES (?, ?, ?, ?, ?)',
          [id, image.image_url || image, image.alt_text || finalTitle, i + 1, i === 0 ? 1 : 0]
        );
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating cabinet:', error);
    res.status(500).json({ error: 'Failed to update cabinet' });
  }
});

// Delete cabinet
app.delete('/api/cabinets/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Delete cabinet (images will be deleted automatically due to CASCADE)
    await dbRun('DELETE FROM cabinets WHERE id = ?', [id]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting cabinet:', error);
    res.status(500).json({ error: 'Failed to delete cabinet' });
  }
});

// ==================== CATEGORIES API ====================

// Get all categories
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await dbAll('SELECT * FROM categories WHERE is_active = 1 ORDER BY type, name');
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// ==================== WHY CHOOSE US API ====================

// Get why choose us data
app.get('/api/why-choose-us', async (req, res) => {
  try {
    const whyChooseUs = await dbGet('SELECT * FROM why_choose_us WHERE is_active = 1 ORDER BY id DESC LIMIT 1');

    if (whyChooseUs) {
      whyChooseUs.features = await dbAll(
        'SELECT * FROM why_choose_us_features WHERE why_choose_us_id = ? AND is_active = 1 ORDER BY sort_order',
        [whyChooseUs.id]
      );
    }

    res.json(whyChooseUs || {});
  } catch (error) {
    console.error('Error fetching why choose us data:', error);
    res.status(500).json({ error: 'Failed to fetch why choose us data' });
  }
});

// Update why choose us data
app.put('/api/why-choose-us', async (req, res) => {
  try {
    const { title, subtitle, features } = req.body;

    // Check if why choose us record exists
    let whyChooseUs = await dbGet('SELECT id FROM why_choose_us WHERE is_active = 1 LIMIT 1');

    if (whyChooseUs) {
      // Update existing record
      await dbRun(
        'UPDATE why_choose_us SET title = ?, subtitle = ?, updated_at = datetime("now") WHERE id = ?',
        [title, subtitle, whyChooseUs.id]
      );
    } else {
      // Create new record
      const result = await dbRun(
        'INSERT INTO why_choose_us (title, subtitle, is_active) VALUES (?, ?, 1)',
        [title, subtitle]
      );
      whyChooseUs = { id: result.id };
    }

    // Update features
    if (features && Array.isArray(features)) {
      // Delete existing features
      await dbRun('DELETE FROM why_choose_us_features WHERE why_choose_us_id = ?', [whyChooseUs.id]);

      // Insert new features
      for (let i = 0; i < features.length; i++) {
        const feature = features[i];
        await dbRun(
          'INSERT INTO why_choose_us_features (why_choose_us_id, title, description, icon, gradient, bg_gradient, number, subtitle, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)',
          [whyChooseUs.id, feature.title, feature.description, feature.icon, feature.gradient, feature.bg_gradient, feature.number, feature.subtitle, i + 1]
        );
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating why choose us data:', error);
    res.status(500).json({ error: 'Failed to update why choose us data' });
  }
});

// ==================== FOOTER API ====================

// Get footer data
app.get('/api/footer', async (req, res) => {
  try {
    const [socialMedia, quickLinks, contactInfo, footerSettings] = await Promise.all([
      dbAll('SELECT * FROM social_media WHERE is_active = 1 ORDER BY sort_order'),
      dbAll('SELECT * FROM quick_links WHERE is_active = 1 ORDER BY sort_order'),
      dbAll('SELECT * FROM contact_info WHERE is_active = 1 ORDER BY sort_order'),
      dbGet('SELECT * FROM footer_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1')
    ]);

    res.json({
      socialMedia,
      quickLinks,
      contactInfo,
      copyright: footerSettings?.copyright_text || '© 2024 خبرة المطابخ. جميع الحقوق محفوظة.'
    });
  } catch (error) {
    console.error('Error fetching footer data:', error);
    res.status(500).json({ error: 'Failed to fetch footer data' });
  }
});

// Update footer data
app.put('/api/footer', async (req, res) => {
  try {
    const { socialMedia, quickLinks, contactInfo, copyright } = req.body;

    // Update social media
    if (socialMedia && Array.isArray(socialMedia)) {
      // Delete existing social media
      await dbRun('DELETE FROM social_media');

      // Insert new social media
      for (let i = 0; i < socialMedia.length; i++) {
        const social = socialMedia[i];
        await dbRun(
          'INSERT INTO social_media (platform, url, icon, sort_order, is_active) VALUES (?, ?, ?, ?, 1)',
          [social.platform, social.url, social.icon, i + 1]
        );
      }
    }

    // Update quick links
    if (quickLinks && Array.isArray(quickLinks)) {
      // Delete existing quick links
      await dbRun('DELETE FROM quick_links');

      // Insert new quick links
      for (let i = 0; i < quickLinks.length; i++) {
        const link = quickLinks[i];
        await dbRun(
          'INSERT INTO quick_links (text, href, sort_order, is_active) VALUES (?, ?, ?, 1)',
          [link.text, link.href, i + 1]
        );
      }
    }

    // Update contact info
    if (contactInfo && Array.isArray(contactInfo)) {
      // Delete existing contact info
      await dbRun('DELETE FROM contact_info');

      // Insert new contact info
      for (let i = 0; i < contactInfo.length; i++) {
        const contact = contactInfo[i];
        await dbRun(
          'INSERT INTO contact_info (icon, text, sort_order, is_active) VALUES (?, ?, ?, 1)',
          [contact.icon, contact.text, i + 1]
        );
      }
    }

    // Update copyright
    if (copyright) {
      // Check if footer settings exist
      const existingSettings = await dbGet('SELECT id FROM footer_settings WHERE is_active = 1 LIMIT 1');

      if (existingSettings) {
        await dbRun(
          'UPDATE footer_settings SET copyright_text = ?, updated_at = datetime("now") WHERE id = ?',
          [copyright, existingSettings.id]
        );
      } else {
        await dbRun(
          'INSERT INTO footer_settings (copyright_text, is_active) VALUES (?, 1)',
          [copyright]
        );
      }
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error updating footer data:', error);
    res.status(500).json({ error: 'Failed to update footer data' });
  }
});

// ==================== COMPANY SETTINGS API ====================

// Get company settings
app.get('/api/company-settings', async (req, res) => {
  try {
    const settings = await dbAll('SELECT setting_key, setting_value FROM company_settings');
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.setting_key] = setting.setting_value;
    });
    res.json(settingsObj);
  } catch (error) {
    console.error('Error fetching company settings:', error);
    res.status(500).json({ error: 'Failed to fetch company settings' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Server running on http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Closing database connection...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err.message);
    } else {
      console.log('✅ Database connection closed');
    }
    process.exit(0);
  });
});
