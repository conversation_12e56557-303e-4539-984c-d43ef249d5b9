"use strict";(()=>{var a={};a.id=286,a.ids=[286],a.modules={1238:(a,b,c)=>{c.r(b),c.d(b,{config:()=>m,default:()=>l,handler:()=>o});var d={};c.r(d),c.d(d,{default:()=>i});var e=c(9046),f=c(8667),g=c(3480),h=c(6435);async function i(a,b){let{slug:c}=a.query,d=Array.isArray(c)?c.join("/"):c,e=`http://localhost:3002/api/${d}`;try{let c=await fetch(e,{method:a.method,headers:{"Content-Type":"application/json","User-Agent":"Next.js API Proxy"},body:"GET"!==a.method?JSON.stringify(a.body):void 0}),d=await c.json();b.status(c.status).json(d)}catch(a){b.status(500).json({error:"Internal Server Error",message:"Failed to connect to API server",details:a.message})}}var j=c(8112),k=c(8766);let l=(0,h.M)(d,"default"),m=(0,h.M)(d,"config"),n=new g.PagesAPIRouteModule({definition:{kind:f.A.PAGES_API,page:"/api/[...slug]",pathname:"/api/[...slug]",bundlePath:"",filename:""},userland:d,distDir:".next",projectDir:""});async function o(a,b,c){let d=await n.prepare(a,b,{srcPage:"/api/[...slug]"});if(!d){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{query:f,params:g,prerenderManifest:h}=d;try{let c=a.method||"GET",d=(0,j.getTracer)(),e=d.getActiveScopeSpan(),i=n.instrumentationOnRequestError.bind(n),l=async e=>n.render(a,b,{query:{...f,...g},params:g,allowedRevalidateHeaderKeys:void 0,multiZoneDraftMode:!0,trustHostHeader:void 0,previewProps:h.preview,propagateError:!1,dev:n.isDev,page:"/api/[...slug]",projectDir:"",onError:(...b)=>i(a,...b)}).finally(()=>{if(!e)return;e.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let f=d.getRootSpanAttributes();if(!f)return;if(f.get("next.span_type")!==k.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${f.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let g=f.get("next.route");if(g){let a=`${c} ${g}`;e.setAttributes({"next.route":g,"http.route":g,"next.span_name":a}),e.updateName(a)}else e.updateName(`${c} ${a.url}`)});e?await l(e):await d.withPropagatedContext(a.headers,()=>d.trace(k.BaseServerSpan.handleRequest,{spanName:`${c} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":c,"http.target":a.url}},l))}catch(a){if(n.isDev)throw a;(0,e.sendError)(b,500,"Internal Server Error")}finally{null==c.waitUntil||c.waitUntil.call(c,Promise.resolve())}}},5600:a=>{a.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var b=require("../../webpack-api-runtime.js");b.C(a);var c=b.X(0,[169],()=>b(b.s=1238));module.exports=c})();