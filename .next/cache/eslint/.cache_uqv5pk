[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37", "/var/www/html/src/components/mobile/MobileAbout.jsx": "38", "/var/www/html/src/components/mobile/MobileBottomNav.jsx": "39", "/var/www/html/src/components/mobile/MobileCabinets.jsx": "40", "/var/www/html/src/components/mobile/MobileContact.jsx": "41", "/var/www/html/src/components/mobile/MobileHeader.jsx": "42", "/var/www/html/src/components/mobile/MobileHome.jsx": "43", "/var/www/html/src/components/mobile/MobileKitchens.jsx": "44", "/var/www/html/src/components/mobile/MobileLayout.jsx": "45", "/var/www/html/src/components/mobile/MobileProductModal.jsx": "46", "/var/www/html/src/utils/performance.js": "47"}, {"size": 463, "mtime": 1752635557499, "results": "48", "hashOfConfig": "49"}, {"size": 1725, "mtime": 1752631575132, "results": "50", "hashOfConfig": "49"}, {"size": 3856, "mtime": 1752709117594, "results": "51", "hashOfConfig": "49"}, {"size": 1269, "mtime": 1752634460814, "results": "52", "hashOfConfig": "49"}, {"size": 477, "mtime": 1752634978610, "results": "53", "hashOfConfig": "49"}, {"size": 475, "mtime": 1752634943717, "results": "54", "hashOfConfig": "49"}, {"size": 645, "mtime": 1752634586689, "results": "55", "hashOfConfig": "49"}, {"size": 477, "mtime": 1752634960134, "results": "56", "hashOfConfig": "49"}, {"size": 482, "mtime": 1752634995310, "results": "57", "hashOfConfig": "49"}, {"size": 16812, "mtime": 1752645642196, "results": "58", "hashOfConfig": "49"}, {"size": 4578, "mtime": 1752704300372, "results": "59", "hashOfConfig": "49"}, {"size": 17055, "mtime": 1752645598689, "results": "60", "hashOfConfig": "49"}, {"size": 3566, "mtime": 1751444435516, "results": "61", "hashOfConfig": "49"}, {"size": 5952, "mtime": 1751443429426, "results": "62", "hashOfConfig": "49"}, {"size": 9709, "mtime": 1751561588128, "results": "63", "hashOfConfig": "49"}, {"size": 10324, "mtime": 1751443430306, "results": "64", "hashOfConfig": "49"}, {"size": 18409, "mtime": 1751686925506, "results": "65", "hashOfConfig": "49"}, {"size": 9439, "mtime": 1751596455083, "results": "66", "hashOfConfig": "49"}, {"size": 17788, "mtime": 1751601289625, "results": "67", "hashOfConfig": "49"}, {"size": 11914, "mtime": 1751667340164, "results": "68", "hashOfConfig": "49"}, {"size": 17940, "mtime": 1751686839673, "results": "69", "hashOfConfig": "49"}, {"size": 14750, "mtime": 1751443432969, "results": "70", "hashOfConfig": "49"}, {"size": 12975, "mtime": 1751601688079, "results": "71", "hashOfConfig": "49"}, {"size": 3828, "mtime": 1751478236235, "results": "72", "hashOfConfig": "49"}, {"size": 10376, "mtime": 1751606566385, "results": "73", "hashOfConfig": "49"}, {"size": 4902, "mtime": 1752709150899, "results": "74", "hashOfConfig": "49"}, {"size": 28308, "mtime": 1752710560401, "results": "75", "hashOfConfig": "49"}, {"size": 923, "mtime": 1751443427609, "results": "76", "hashOfConfig": "49"}, {"size": 6319, "mtime": 1752645029055, "results": "77", "hashOfConfig": "49"}, {"size": 4282, "mtime": 1752643007566, "results": "78", "hashOfConfig": "49"}, {"size": 31159, "mtime": 1752710311176, "results": "79", "hashOfConfig": "49"}, {"size": 16900, "mtime": 1752710588247, "results": "80", "hashOfConfig": "49"}, {"size": 5001, "mtime": 1752632019825, "results": "81", "hashOfConfig": "49"}, {"size": 3517, "mtime": 1751443429007, "results": "82", "hashOfConfig": "49"}, {"size": 9400, "mtime": 1751696106894, "results": "83", "hashOfConfig": "49"}, {"size": 1438, "mtime": 1752708846464, "results": "84", "hashOfConfig": "49"}, {"size": 5079, "mtime": 1752632642474, "results": "85", "hashOfConfig": "49"}, {"size": 7865, "mtime": 1752705598884, "results": "86", "hashOfConfig": "49"}, {"size": 3314, "mtime": 1752646795554, "results": "87", "hashOfConfig": "49"}, {"size": 8918, "mtime": 1752707917372, "results": "88", "hashOfConfig": "49"}, {"size": 8818, "mtime": 1752707064396, "results": "89", "hashOfConfig": "49"}, {"size": 2067, "mtime": 1752704615289, "results": "90", "hashOfConfig": "49"}, {"size": 12425, "mtime": 1752707390469, "results": "91", "hashOfConfig": "49"}, {"size": 8907, "mtime": 1752707892765, "results": "92", "hashOfConfig": "49"}, {"size": 4978, "mtime": 1752707633164, "results": "93", "hashOfConfig": "49"}, {"size": 11083, "mtime": 1752707311184, "results": "94", "hashOfConfig": "49"}, {"size": 3501, "mtime": 1752707672068, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["237", "238", "239", "240"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["241", "242", "243", "244"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["245", "246"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["247", "248"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["249"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["250", "251", "252", "253"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["254"], [], "/var/www/html/src/components/HeroSection.jsx", ["255"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["256", "257", "258", "259"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["260"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/components/mobile/MobileAbout.jsx", [], [], "/var/www/html/src/components/mobile/MobileBottomNav.jsx", [], [], "/var/www/html/src/components/mobile/MobileCabinets.jsx", [], [], "/var/www/html/src/components/mobile/MobileContact.jsx", [], [], "/var/www/html/src/components/mobile/MobileHeader.jsx", [], [], "/var/www/html/src/components/mobile/MobileHome.jsx", [], [], "/var/www/html/src/components/mobile/MobileKitchens.jsx", [], [], "/var/www/html/src/components/mobile/MobileLayout.jsx", [], [], "/var/www/html/src/components/mobile/MobileProductModal.jsx", [], [], "/var/www/html/src/utils/performance.js", [], [], {"ruleId": "261", "severity": 1, "message": "262", "line": 75, "column": 6, "nodeType": "263", "endLine": 75, "endColumn": 8, "suggestions": "264"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 127, "column": 21, "nodeType": "267", "endLine": 131, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "266", "line": 178, "column": 29, "nodeType": "267", "endLine": 182, "endColumn": 31}, {"ruleId": "265", "severity": 1, "message": "266", "line": 199, "column": 31, "nodeType": "267", "endLine": 203, "endColumn": 33}, {"ruleId": "261", "severity": 1, "message": "268", "line": 78, "column": 6, "nodeType": "263", "endLine": 78, "endColumn": 8, "suggestions": "269"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 130, "column": 21, "nodeType": "267", "endLine": 134, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "266", "line": 181, "column": 29, "nodeType": "267", "endLine": 185, "endColumn": 31}, {"ruleId": "265", "severity": 1, "message": "266", "line": 202, "column": 31, "nodeType": "267", "endLine": 206, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "266", "line": 285, "column": 15, "nodeType": "267", "endLine": 289, "endColumn": 17}, {"ruleId": "265", "severity": 1, "message": "266", "line": 318, "column": 23, "nodeType": "267", "endLine": 318, "endColumn": 101}, {"ruleId": "265", "severity": 1, "message": "266", "line": 277, "column": 15, "nodeType": "267", "endLine": 281, "endColumn": 17}, {"ruleId": "265", "severity": 1, "message": "266", "line": 310, "column": 23, "nodeType": "267", "endLine": 310, "endColumn": 101}, {"ruleId": "270", "severity": 1, "message": "271", "line": 154, "column": 1, "nodeType": "272", "endLine": 160, "endColumn": 2}, {"ruleId": "261", "severity": 1, "message": "273", "line": 104, "column": 6, "nodeType": "263", "endLine": 104, "endColumn": 8, "suggestions": "274"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 287, "column": 23, "nodeType": "267", "endLine": 291, "endColumn": 25}, {"ruleId": "265", "severity": 1, "message": "266", "line": 500, "column": 27, "nodeType": "267", "endLine": 504, "endColumn": 29}, {"ruleId": "265", "severity": 1, "message": "266", "line": 515, "column": 19, "nodeType": "267", "endLine": 519, "endColumn": 21}, {"ruleId": "261", "severity": 1, "message": "275", "line": 58, "column": 6, "nodeType": "263", "endLine": 58, "endColumn": 8, "suggestions": "276"}, {"ruleId": "261", "severity": 1, "message": "277", "line": 47, "column": 6, "nodeType": "263", "endLine": 47, "endColumn": 8, "suggestions": "278"}, {"ruleId": "261", "severity": 1, "message": "279", "line": 106, "column": 6, "nodeType": "263", "endLine": 106, "endColumn": 8, "suggestions": "280"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 408, "column": 19, "nodeType": "267", "endLine": 412, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "266", "line": 577, "column": 27, "nodeType": "267", "endLine": 581, "endColumn": 29}, {"ruleId": "265", "severity": 1, "message": "266", "line": 592, "column": 19, "nodeType": "267", "endLine": 596, "endColumn": 21}, {"ruleId": "261", "severity": 1, "message": "281", "line": 99, "column": 6, "nodeType": "263", "endLine": 99, "endColumn": 8, "suggestions": "282"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["283"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["284"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultCabinets'. Either include it or remove the dependency array.", ["285"], "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["286"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["287"], "React Hook useEffect has a missing dependency: 'defaultKitchens'. Either include it or remove the dependency array.", ["288"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["289"], {"desc": "290", "fix": "291"}, {"desc": "292", "fix": "293"}, {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, {"desc": "298", "fix": "299"}, {"desc": "300", "fix": "301"}, {"desc": "302", "fix": "303"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "304", "text": "305"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "306", "text": "307"}, "Update the dependencies array to be: [defaultCabinets]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [default<PERSON><PERSON>ens]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "316", "text": "317"}, [2924, 2926], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [3820, 3822], "[defaultCabinets]", [2493, 2495], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3944, 3946], "[default<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]