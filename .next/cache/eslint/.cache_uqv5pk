[{"/var/www/html/pages/_app.js": "1", "/var/www/html/pages/_document.js": "2", "/var/www/html/pages/admin.js": "3", "/var/www/html/pages/api/[...slug].js": "4", "/var/www/html/pages/api/cabinets.js": "5", "/var/www/html/pages/api/footer.js": "6", "/var/www/html/pages/api/hero.js": "7", "/var/www/html/pages/api/kitchens.js": "8", "/var/www/html/pages/api/why-choose-us.js": "9", "/var/www/html/pages/cabinets.js": "10", "/var/www/html/pages/index.js": "11", "/var/www/html/pages/kitchens.js": "12", "/var/www/html/src/admin/components/Dashboard.jsx": "13", "/var/www/html/src/admin/components/Header.jsx": "14", "/var/www/html/src/admin/components/Login.jsx": "15", "/var/www/html/src/admin/components/Sidebar.jsx": "16", "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx": "17", "/var/www/html/src/admin/components/sections/DashboardHome.jsx": "18", "/var/www/html/src/admin/components/sections/FooterManagement.jsx": "19", "/var/www/html/src/admin/components/sections/HeroManagement.jsx": "20", "/var/www/html/src/admin/components/sections/KitchensManagement.jsx": "21", "/var/www/html/src/admin/components/sections/UserManagement.jsx": "22", "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx": "23", "/var/www/html/src/admin/context/AuthContext.jsx": "24", "/var/www/html/src/admin/context/DataContext.jsx": "25", "/var/www/html/src/admin/utils/initDatabase.js": "26", "/var/www/html/src/components/CabinetGallery.jsx": "27", "/var/www/html/src/components/CallToAction.jsx": "28", "/var/www/html/src/components/Footer.jsx": "29", "/var/www/html/src/components/HeroSection.jsx": "30", "/var/www/html/src/components/KitchenGallery.jsx": "31", "/var/www/html/src/components/Navbar.jsx": "32", "/var/www/html/src/components/SEO.jsx": "33", "/var/www/html/src/components/Testimonials.jsx": "34", "/var/www/html/src/components/WhyChooseUs.jsx": "35", "/var/www/html/src/config/api.js": "36", "/var/www/html/src/config/env.js": "37", "/var/www/html/src/components/mobile/MobileAbout.jsx": "38", "/var/www/html/src/components/mobile/MobileBottomNav.jsx": "39", "/var/www/html/src/components/mobile/MobileCabinets.jsx": "40", "/var/www/html/src/components/mobile/MobileContact.jsx": "41", "/var/www/html/src/components/mobile/MobileHeader.jsx": "42", "/var/www/html/src/components/mobile/MobileHome.jsx": "43", "/var/www/html/src/components/mobile/MobileKitchens.jsx": "44", "/var/www/html/src/components/mobile/MobileLayout.jsx": "45", "/var/www/html/src/components/mobile/MobileProductModal.jsx": "46", "/var/www/html/src/utils/performance.js": "47", "/var/www/html/src/admin/hooks/useImageOptimization.js": "48"}, {"size": 463, "mtime": 1752635557499, "results": "49", "hashOfConfig": "50"}, {"size": 1725, "mtime": 1752631575132, "results": "51", "hashOfConfig": "50"}, {"size": 3856, "mtime": 1752709117594, "results": "52", "hashOfConfig": "50"}, {"size": 1269, "mtime": 1752634460814, "results": "53", "hashOfConfig": "50"}, {"size": 477, "mtime": 1752634978610, "results": "54", "hashOfConfig": "50"}, {"size": 475, "mtime": 1752634943717, "results": "55", "hashOfConfig": "50"}, {"size": 645, "mtime": 1752634586689, "results": "56", "hashOfConfig": "50"}, {"size": 477, "mtime": 1752634960134, "results": "57", "hashOfConfig": "50"}, {"size": 482, "mtime": 1752634995310, "results": "58", "hashOfConfig": "50"}, {"size": 16812, "mtime": 1752645642196, "results": "59", "hashOfConfig": "50"}, {"size": 4578, "mtime": 1752704300372, "results": "60", "hashOfConfig": "50"}, {"size": 17055, "mtime": 1752645598689, "results": "61", "hashOfConfig": "50"}, {"size": 3566, "mtime": 1751444435516, "results": "62", "hashOfConfig": "50"}, {"size": 5952, "mtime": 1751443429426, "results": "63", "hashOfConfig": "50"}, {"size": 9709, "mtime": 1751561588128, "results": "64", "hashOfConfig": "50"}, {"size": 10324, "mtime": 1751443430306, "results": "65", "hashOfConfig": "50"}, {"size": 23690, "mtime": 1753916204320, "results": "66", "hashOfConfig": "50"}, {"size": 9439, "mtime": 1751596455083, "results": "67", "hashOfConfig": "50"}, {"size": 17788, "mtime": 1751601289625, "results": "68", "hashOfConfig": "50"}, {"size": 13960, "mtime": 1753916333182, "results": "69", "hashOfConfig": "50"}, {"size": 18786, "mtime": 1753916244580, "results": "70", "hashOfConfig": "50"}, {"size": 14750, "mtime": 1751443432969, "results": "71", "hashOfConfig": "50"}, {"size": 12975, "mtime": 1751601688079, "results": "72", "hashOfConfig": "50"}, {"size": 3828, "mtime": 1751478236235, "results": "73", "hashOfConfig": "50"}, {"size": 10376, "mtime": 1751606566385, "results": "74", "hashOfConfig": "50"}, {"size": 4902, "mtime": 1752709150899, "results": "75", "hashOfConfig": "50"}, {"size": 28308, "mtime": 1752710560401, "results": "76", "hashOfConfig": "50"}, {"size": 923, "mtime": 1751443427609, "results": "77", "hashOfConfig": "50"}, {"size": 6319, "mtime": 1752645029055, "results": "78", "hashOfConfig": "50"}, {"size": 4282, "mtime": 1752643007566, "results": "79", "hashOfConfig": "50"}, {"size": 31159, "mtime": 1752710311176, "results": "80", "hashOfConfig": "50"}, {"size": 16900, "mtime": 1752710588247, "results": "81", "hashOfConfig": "50"}, {"size": 5001, "mtime": 1752632019825, "results": "82", "hashOfConfig": "50"}, {"size": 3517, "mtime": 1751443429007, "results": "83", "hashOfConfig": "50"}, {"size": 9400, "mtime": 1751696106894, "results": "84", "hashOfConfig": "50"}, {"size": 1438, "mtime": 1752708846464, "results": "85", "hashOfConfig": "50"}, {"size": 5079, "mtime": 1752632642474, "results": "86", "hashOfConfig": "50"}, {"size": 7865, "mtime": 1752705598884, "results": "87", "hashOfConfig": "50"}, {"size": 3314, "mtime": 1752646795554, "results": "88", "hashOfConfig": "50"}, {"size": 8918, "mtime": 1752707917372, "results": "89", "hashOfConfig": "50"}, {"size": 8818, "mtime": 1752707064396, "results": "90", "hashOfConfig": "50"}, {"size": 2067, "mtime": 1752704615289, "results": "91", "hashOfConfig": "50"}, {"size": 12425, "mtime": 1752707390469, "results": "92", "hashOfConfig": "50"}, {"size": 8907, "mtime": 1752707892765, "results": "93", "hashOfConfig": "50"}, {"size": 4978, "mtime": 1752707633164, "results": "94", "hashOfConfig": "50"}, {"size": 11083, "mtime": 1752707311184, "results": "95", "hashOfConfig": "50"}, {"size": 3501, "mtime": 1752707672068, "results": "96", "hashOfConfig": "50"}, {"size": 6709, "mtime": 1753916439499, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lomtko", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/pages/_app.js", [], [], "/var/www/html/pages/_document.js", [], [], "/var/www/html/pages/admin.js", [], [], "/var/www/html/pages/api/[...slug].js", [], [], "/var/www/html/pages/api/cabinets.js", [], [], "/var/www/html/pages/api/footer.js", [], [], "/var/www/html/pages/api/hero.js", [], [], "/var/www/html/pages/api/kitchens.js", [], [], "/var/www/html/pages/api/why-choose-us.js", [], [], "/var/www/html/pages/cabinets.js", ["242", "243", "244", "245"], [], "/var/www/html/pages/index.js", [], [], "/var/www/html/pages/kitchens.js", ["246", "247", "248", "249"], [], "/var/www/html/src/admin/components/Dashboard.jsx", [], [], "/var/www/html/src/admin/components/Header.jsx", [], [], "/var/www/html/src/admin/components/Login.jsx", [], [], "/var/www/html/src/admin/components/Sidebar.jsx", [], [], "/var/www/html/src/admin/components/sections/CabinetsManagement.jsx", ["250", "251", "252", "253"], [], "/var/www/html/src/admin/components/sections/DashboardHome.jsx", [], [], "/var/www/html/src/admin/components/sections/FooterManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/HeroManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/KitchensManagement.jsx", ["254", "255"], [], "/var/www/html/src/admin/components/sections/UserManagement.jsx", [], [], "/var/www/html/src/admin/components/sections/WhyChooseUsManagement.jsx", [], [], "/var/www/html/src/admin/context/AuthContext.jsx", [], [], "/var/www/html/src/admin/context/DataContext.jsx", [], [], "/var/www/html/src/admin/utils/initDatabase.js", ["256"], [], "/var/www/html/src/components/CabinetGallery.jsx", ["257", "258", "259", "260"], [], "/var/www/html/src/components/CallToAction.jsx", [], [], "/var/www/html/src/components/Footer.jsx", ["261"], [], "/var/www/html/src/components/HeroSection.jsx", ["262"], [], "/var/www/html/src/components/KitchenGallery.jsx", ["263", "264", "265", "266"], [], "/var/www/html/src/components/Navbar.jsx", [], [], "/var/www/html/src/components/SEO.jsx", [], [], "/var/www/html/src/components/Testimonials.jsx", [], [], "/var/www/html/src/components/WhyChooseUs.jsx", ["267"], [], "/var/www/html/src/config/api.js", [], [], "/var/www/html/src/config/env.js", [], [], "/var/www/html/src/components/mobile/MobileAbout.jsx", [], [], "/var/www/html/src/components/mobile/MobileBottomNav.jsx", [], [], "/var/www/html/src/components/mobile/MobileCabinets.jsx", [], [], "/var/www/html/src/components/mobile/MobileContact.jsx", [], [], "/var/www/html/src/components/mobile/MobileHeader.jsx", [], [], "/var/www/html/src/components/mobile/MobileHome.jsx", [], [], "/var/www/html/src/components/mobile/MobileKitchens.jsx", [], [], "/var/www/html/src/components/mobile/MobileLayout.jsx", [], [], "/var/www/html/src/components/mobile/MobileProductModal.jsx", [], [], "/var/www/html/src/utils/performance.js", [], [], "/var/www/html/src/admin/hooks/useImageOptimization.js", [], [], {"ruleId": "268", "severity": 1, "message": "269", "line": 75, "column": 6, "nodeType": "270", "endLine": 75, "endColumn": 8, "suggestions": "271"}, {"ruleId": "272", "severity": 1, "message": "273", "line": 127, "column": 21, "nodeType": "274", "endLine": 131, "endColumn": 23}, {"ruleId": "272", "severity": 1, "message": "273", "line": 178, "column": 29, "nodeType": "274", "endLine": 182, "endColumn": 31}, {"ruleId": "272", "severity": 1, "message": "273", "line": 199, "column": 31, "nodeType": "274", "endLine": 203, "endColumn": 33}, {"ruleId": "268", "severity": 1, "message": "275", "line": 78, "column": 6, "nodeType": "270", "endLine": 78, "endColumn": 8, "suggestions": "276"}, {"ruleId": "272", "severity": 1, "message": "273", "line": 130, "column": 21, "nodeType": "274", "endLine": 134, "endColumn": 23}, {"ruleId": "272", "severity": 1, "message": "273", "line": 181, "column": 29, "nodeType": "274", "endLine": 185, "endColumn": 31}, {"ruleId": "272", "severity": 1, "message": "273", "line": 202, "column": 31, "nodeType": "274", "endLine": 206, "endColumn": 33}, {"ruleId": "268", "severity": 1, "message": "277", "line": 44, "column": 27, "nodeType": "278", "endLine": 44, "endColumn": 38}, {"ruleId": "268", "severity": 1, "message": "279", "line": 60, "column": 6, "nodeType": "270", "endLine": 60, "endColumn": 32, "suggestions": "280"}, {"ruleId": "272", "severity": 1, "message": "273", "line": 358, "column": 15, "nodeType": "274", "endLine": 362, "endColumn": 17}, {"ruleId": "272", "severity": 1, "message": "273", "line": 391, "column": 23, "nodeType": "274", "endLine": 391, "endColumn": 101}, {"ruleId": "272", "severity": 1, "message": "273", "line": 293, "column": 15, "nodeType": "274", "endLine": 297, "endColumn": 17}, {"ruleId": "272", "severity": 1, "message": "273", "line": 326, "column": 23, "nodeType": "274", "endLine": 326, "endColumn": 101}, {"ruleId": "281", "severity": 1, "message": "282", "line": 154, "column": 1, "nodeType": "283", "endLine": 160, "endColumn": 2}, {"ruleId": "268", "severity": 1, "message": "284", "line": 104, "column": 6, "nodeType": "270", "endLine": 104, "endColumn": 8, "suggestions": "285"}, {"ruleId": "272", "severity": 1, "message": "273", "line": 287, "column": 23, "nodeType": "274", "endLine": 291, "endColumn": 25}, {"ruleId": "272", "severity": 1, "message": "273", "line": 500, "column": 27, "nodeType": "274", "endLine": 504, "endColumn": 29}, {"ruleId": "272", "severity": 1, "message": "273", "line": 515, "column": 19, "nodeType": "274", "endLine": 519, "endColumn": 21}, {"ruleId": "268", "severity": 1, "message": "286", "line": 58, "column": 6, "nodeType": "270", "endLine": 58, "endColumn": 8, "suggestions": "287"}, {"ruleId": "268", "severity": 1, "message": "288", "line": 47, "column": 6, "nodeType": "270", "endLine": 47, "endColumn": 8, "suggestions": "289"}, {"ruleId": "268", "severity": 1, "message": "290", "line": 106, "column": 6, "nodeType": "270", "endLine": 106, "endColumn": 8, "suggestions": "291"}, {"ruleId": "272", "severity": 1, "message": "273", "line": 408, "column": 19, "nodeType": "274", "endLine": 412, "endColumn": 21}, {"ruleId": "272", "severity": 1, "message": "273", "line": 577, "column": 27, "nodeType": "274", "endLine": 581, "endColumn": 29}, {"ruleId": "272", "severity": 1, "message": "273", "line": 592, "column": 19, "nodeType": "274", "endLine": 596, "endColumn": 21}, {"ruleId": "268", "severity": 1, "message": "292", "line": 99, "column": 6, "nodeType": "270", "endLine": 99, "endColumn": 8, "suggestions": "293"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fallbackCabinets'. Either include it or remove the dependency array.", "ArrayExpression", ["294"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fallbackKitchens'. Either include it or remove the dependency array.", ["295"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useMemo has a missing dependency: 'getCategoryName'. Either include it or remove the dependency array.", ["296"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'defaultCabinets'. Either include it or remove the dependency array.", ["297"], "React Hook useEffect has a missing dependency: 'defaultFooterData'. Either include it or remove the dependency array.", ["298"], "React Hook useEffect has a missing dependency: 'defaultHeroData'. Either include it or remove the dependency array.", ["299"], "React Hook useEffect has a missing dependency: 'defaultKitchens'. Either include it or remove the dependency array.", ["300"], "React Hook useEffect has a missing dependency: 'defaultWhyChooseUsData'. Either include it or remove the dependency array.", ["301"], {"desc": "302", "fix": "303"}, {"desc": "304", "fix": "305"}, {"desc": "306", "fix": "307"}, {"desc": "308", "fix": "309"}, {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, {"desc": "314", "fix": "315"}, {"desc": "316", "fix": "317"}, "Update the dependencies array to be: [fallbackCabinets]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [fallback<PERSON><PERSON><PERSON>]", {"range": "320", "text": "321"}, "Update the dependencies array to be: [cabinetsData, getCategoryName, searchTerm]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [defaultCabinets]", {"range": "324", "text": "325"}, "Update the dependencies array to be: [defaultFooterData]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [defaultHeroData]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [default<PERSON><PERSON>ens]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [defaultWhyChooseUsData]", {"range": "332", "text": "333"}, [2924, 2926], "[fallbackCabinets]", [3099, 3101], "[fallback<PERSON><PERSON><PERSON>]", [2035, 2061], "[cabinetsData, getCategoryName, searchTerm]", [3820, 3822], "[defaultCabinets]", [2493, 2495], "[defaultFooterData]", [1825, 1827], "[defaultHeroData]", [3944, 3946], "[default<PERSON><PERSON><PERSON>]", [3175, 3177], "[defaultWhyChooseUsData]"]