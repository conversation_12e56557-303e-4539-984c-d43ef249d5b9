(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{181:(e,t,i)=>{"use strict";i.d(t,{E:()=>r});var s=i(4232);let r=i(2205).B?s.useLayoutEffect:s.useEffect},373:(e,t,i)=>{"use strict";function s(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let s=document;t&&(s=t.current);let r=i?.[e]??s.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}i.d(t,{K:()=>s})},508:(e,t,i)=>{"use strict";i.d(t,{W:()=>a});var s=i(4232),r=i(373);let n={some:0,all:1};function a(e,{root:t,margin:i,amount:o,once:l=!1,initial:d=!1}={}){let[u,h]=(0,s.useState)(d);return(0,s.useEffect)(()=>{if(!e.current||l&&u)return;let s={root:t&&t.current||void 0,margin:i,amount:o};return function(e,t,{root:i,margin:s,amount:a="some"}={}){let o=(0,r.K)(e),l=new WeakMap,d=new IntersectionObserver(e=>{e.forEach(e=>{let i=l.get(e.target);if(!!i!==e.isIntersecting)if(e.isIntersecting){let i=t(e.target,e);"function"==typeof i?l.set(e.target,i):d.unobserve(e.target)}else"function"==typeof i&&(i(e),l.delete(e.target))})},{root:i,rootMargin:s,threshold:"number"==typeof a?a:n[a]});return o.forEach(e=>d.observe(e)),()=>d.disconnect()}(e.current,()=>(h(!0),l?void 0:()=>h(!1)),s)},[t,e,i,l,o]),u}},1026:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let s=i(4232);function r(e,t){let i=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=i.current;e&&(i.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(i.current=n(e,s)),t&&(r.current=n(t,s))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1200:(e,t,i)=>{"use strict";i.d(t,{M:()=>r});var s=i(4232);function r(e){let t=(0,s.useRef)(null);return null===t.current&&(t.current=e()),t.current}},1639:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return S},useLinkStatus:function(){return x}});let s=i(8365),r=i(7876),n=s._(i(4232)),a=i(6658),o=i(1851),l=i(6225),d=i(8407),u=i(2696),h=i(8265),c=i(2343),p=i(8940),m=i(7469),f=i(1026);i(3724);let g=new Set;function v(e,t,i,s){if((0,o.isLocalURL)(t)){if(!s.bypassPrefetchedCheck){let r=t+"%"+i+"%"+(void 0!==s.locale?s.locale:"locale"in e?e.locale:void 0);if(g.has(r))return;g.add(r)}e.prefetch(t,i,s).catch(e=>{})}}function y(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let w=n.default.forwardRef(function(e,t){let i,s,{href:l,as:g,children:w,prefetch:b=null,passHref:x,replace:S,shallow:T,scroll:E,locale:P,onClick:M,onNavigate:C,onMouseEnter:A,onTouchStart:k,legacyBehavior:L=!1,...O}=e;i=w,L&&("string"==typeof i||"number"==typeof i)&&(i=(0,r.jsx)("a",{children:i}));let D=n.default.useContext(h.RouterContext),V=!1!==b,{href:I,as:R}=n.default.useMemo(()=>{if(!D){let e=y(l);return{href:e,as:g?y(g):e}}let[e,t]=(0,a.resolveHref)(D,l,!0);return{href:e,as:g?(0,a.resolveHref)(D,g):t||e}},[D,l,g]),j=n.default.useRef(I),B=n.default.useRef(R);L&&(s=n.default.Children.only(i));let F=L?s&&"object"==typeof s&&s.ref:t,[_,$,z]=(0,c.useIntersection)({rootMargin:"200px"}),N=n.default.useCallback(e=>{(B.current!==R||j.current!==I)&&(z(),B.current=R,j.current=I),_(e)},[R,I,z,_]),G=(0,f.useMergedRef)(N,F);n.default.useEffect(()=>{D&&$&&V&&v(D,I,R,{locale:P})},[R,I,$,P,V,null==D?void 0:D.locale,D]);let H={ref:G,onClick(e){L||"function"!=typeof M||M(e),L&&s.props&&"function"==typeof s.props.onClick&&s.props.onClick(e),D&&(e.defaultPrevented||function(e,t,i,s,r,n,a,l,d){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,o.isLocalURL)(i)){r&&(e.preventDefault(),location.replace(i));return}e.preventDefault(),(()=>{if(d){let e=!1;if(d({preventDefault:()=>{e=!0}}),e)return}let e=null==a||a;"beforePopState"in t?t[r?"replace":"push"](i,s,{shallow:n,locale:l,scroll:e}):t[r?"replace":"push"](s||i,{scroll:e})})()}}(e,D,I,R,S,T,E,P,C))},onMouseEnter(e){L||"function"!=typeof A||A(e),L&&s.props&&"function"==typeof s.props.onMouseEnter&&s.props.onMouseEnter(e),D&&v(D,I,R,{locale:P,priority:!0,bypassPrefetchedCheck:!0})},onTouchStart:function(e){L||"function"!=typeof k||k(e),L&&s.props&&"function"==typeof s.props.onTouchStart&&s.props.onTouchStart(e),D&&v(D,I,R,{locale:P,priority:!0,bypassPrefetchedCheck:!0})}};if((0,d.isAbsoluteUrl)(R))H.href=R;else if(!L||x||"a"===s.type&&!("href"in s.props)){let e=void 0!==P?P:null==D?void 0:D.locale;H.href=(null==D?void 0:D.isLocaleDomain)&&(0,p.getDomainLocale)(R,e,null==D?void 0:D.locales,null==D?void 0:D.domainLocales)||(0,m.addBasePath)((0,u.addLocale)(R,e,null==D?void 0:D.defaultLocale))}return L?n.default.cloneElement(s,H):(0,r.jsx)("a",{...O,...H,children:i})}),b=(0,n.createContext)({pending:!1}),x=()=>(0,n.useContext)(b),S=w;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2161:()=>{},2205:(e,t,i)=>{"use strict";i.d(t,{B:()=>s});let s="undefined"!=typeof window},2343:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let s=i(4232),r=i(4754),n="function"==typeof IntersectionObserver,a=new Map,o=[];function l(e){let{rootRef:t,rootMargin:i,disabled:l}=e,d=l||!n,[u,h]=(0,s.useState)(!1),c=(0,s.useRef)(null),p=(0,s.useCallback)(e=>{c.current=e},[]);return(0,s.useEffect)(()=>{if(n){if(d||u)return;let e=c.current;if(e&&e.tagName)return function(e,t,i){let{id:s,observer:r,elements:n}=function(e){let t,i={root:e.root||null,margin:e.rootMargin||""},s=o.find(e=>e.root===i.root&&e.margin===i.margin);if(s&&(t=a.get(s)))return t;let r=new Map;return t={id:i,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=r.get(e.target),i=e.isIntersecting||e.intersectionRatio>0;t&&i&&t(i)})},e),elements:r},o.push(i),a.set(i,t),t}(i);return n.set(e,t),r.observe(e),function(){if(n.delete(e),r.unobserve(e),0===n.size){r.disconnect(),a.delete(s);let e=o.findIndex(e=>e.root===s.root&&e.margin===s.margin);e>-1&&o.splice(e,1)}}}(e,e=>e&&h(e),{root:null==t?void 0:t.current,rootMargin:i})}else if(!u){let e=(0,r.requestIdleCallback)(()=>h(!0));return()=>(0,r.cancelIdleCallback)(e)}},[d,i,t,u,c.current]),[p,u,(0,s.useCallback)(()=>{h(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2370:(e,t,i)=>{"use strict";function s(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e,t){void 0===e&&(e={}),void 0===t&&(t={});let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:s(t[i])&&s(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])})}i.d(t,{a:()=>l,g:()=>a});let n={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return r(e,n),e}let o={document:n,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function l(){let e="undefined"!=typeof window?window:{};return r(e,o),e}},3724:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},3748:(e,t,i)=>{"use strict";i.d(t,{Ij:()=>d,t9:()=>c,Vx:()=>a,dK:()=>l,WO:()=>u});var s=i(2370),r=i(4569);function n(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(n=>{if(!i[n]&&!0===i.auto){let a=(0,r.e)(e.el,`.${s[n]}`)[0];a||((a=(0,r.c)("div",s[n])).className=s[n],e.el.append(a)),i[n]=a,t[n]=a}}),i}function a(e){let{swiper:t,extendParams:i,on:s,emit:a}=e;function o(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function l(e,i){let s=t.params.navigation;(e=(0,r.m)(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass))})}function d(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){l(i,!1),l(e,!1);return}l(i,t.isBeginning&&!t.params.rewind),l(e,t.isEnd&&!t.params.rewind)}function u(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),a("navigationPrev"))}function h(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),a("navigationNext"))}function c(){let e=t.params.navigation;if(t.params.navigation=n(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=o(e.nextEl),s=o(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:s}),i=(0,r.m)(i),s=(0,r.m)(s);let a=(i,s)=>{i&&i.addEventListener("click","next"===s?h:u),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>a(e,"next")),s.forEach(e=>a(e,"prev"))}function p(){let{nextEl:e,prevEl:i}=t.navigation;e=(0,r.m)(e),i=(0,r.m)(i);let s=(e,i)=>{e.removeEventListener("click","next"===i?h:u),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),i.forEach(e=>s(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},s("init",()=>{!1===t.params.navigation.enabled?m():(c(),d())}),s("toEdge fromEdge lock unlock",()=>{d()}),s("destroy",()=>{p()}),s("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=(0,r.m)(e),i=(0,r.m)(i),t.enabled)return void d();[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),s("click",(e,i)=>{let{nextEl:s,prevEl:n}=t.navigation;s=(0,r.m)(s),n=(0,r.m)(n);let o=i.target,l=n.includes(o)||s.includes(o);if(t.isElement&&!l){let e=i.path||i.composedPath&&i.composedPath();e&&(l=e.find(e=>s.includes(e)||n.includes(e)))}if(t.params.navigation.hideOnClick&&!l){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===o||t.pagination.el.contains(o)))return;s.length?e=s[0].classList.contains(t.params.navigation.hiddenClass):n.length&&(e=n[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?a("navigationShow"):a("navigationHide"),[...s,...n].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let m=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),d()},disable:m,update:d,init:c,destroy:p})}function o(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function l(e){let t,{swiper:i,extendParams:s,on:a,emit:l}=e,d="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${d}-bullet`,bulletActiveClass:`${d}-bullet-active`,modifierClass:`${d}-`,currentClass:`${d}-current`,totalClass:`${d}-total`,hiddenClass:`${d}-hidden`,progressbarFillClass:`${d}-progressbar-fill`,progressbarOppositeClass:`${d}-progressbar-opposite`,clickableClass:`${d}-clickable`,lockClass:`${d}-lock`,horizontalClass:`${d}-horizontal`,verticalClass:`${d}-vertical`,paginationDisabledClass:`${d}-disabled`}}),i.pagination={el:null,bullets:[]};let u=0;function h(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function c(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function p(e){let t=e.target.closest(o(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=(0,r.i)(t)*i.params.slidesPerGroup;if(i.params.loop){var n,a,l;if(i.realIndex===s)return;let e=(n=i.realIndex,a=s,(n%=l=i.slides.length,(a%=l)===n+1)?"next":a===n-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function m(){let e,s,n=i.rtl,a=i.params.pagination;if(h())return;let d=i.pagination.el;d=(0,r.m)(d);let p=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,m=i.params.loop?Math.ceil(p/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(s=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,s=i.previousSnapIndex):(s=i.previousIndex||0,e=i.activeIndex||0),"bullets"===a.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let o,l,h,p=i.pagination.bullets;if(a.dynamicBullets&&(t=(0,r.h)(p[0],i.isHorizontal()?"width":"height",!0),d.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(a.dynamicMainBullets+4)}px`}),a.dynamicMainBullets>1&&void 0!==s&&((u+=e-(s||0))>a.dynamicMainBullets-1?u=a.dynamicMainBullets-1:u<0&&(u=0)),h=((l=(o=Math.max(e-u,0))+(Math.min(p.length,a.dynamicMainBullets)-1))+o)/2),p.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${a.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),d.length>1)p.forEach(t=>{let s=(0,r.i)(t);s===e?t.classList.add(...a.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),a.dynamicBullets&&(s>=o&&s<=l&&t.classList.add(...`${a.bulletActiveClass}-main`.split(" ")),s===o&&c(t,"prev"),s===l&&c(t,"next"))});else{let t=p[e];if(t&&t.classList.add(...a.bulletActiveClass.split(" ")),i.isElement&&p.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),a.dynamicBullets){let e=p[o],t=p[l];for(let e=o;e<=l;e+=1)p[e]&&p[e].classList.add(...`${a.bulletActiveClass}-main`.split(" "));c(e,"prev"),c(t,"next")}}if(a.dynamicBullets){let e=Math.min(p.length,a.dynamicMainBullets+4),s=(t*e-t)/2-h*t,r=n?"right":"left";p.forEach(e=>{e.style[i.isHorizontal()?r:"top"]=`${s}px`})}}d.forEach((t,s)=>{if("fraction"===a.type&&(t.querySelectorAll(o(a.currentClass)).forEach(t=>{t.textContent=a.formatFractionCurrent(e+1)}),t.querySelectorAll(o(a.totalClass)).forEach(e=>{e.textContent=a.formatFractionTotal(m)})),"progressbar"===a.type){let s;s=a.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let r=(e+1)/m,n=1,l=1;"horizontal"===s?n=r:l=r,t.querySelectorAll(o(a.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${n}) scaleY(${l})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===a.type&&a.renderCustom?((0,r.s)(t,a.renderCustom(i,e+1,m)),0===s&&l("paginationRender",t)):(0===s&&l("paginationRender",t),l("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](a.lockClass)})}function f(){let e=i.params.pagination;if(h())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=(0,r.m)(s);let n="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?n+=e.renderBullet.call(i,t,e.bulletClass):n+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(n=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(n=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&(0,r.s)(t,n||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(o(e.bulletClass)))}),"custom"!==e.type&&l("paginationRender",s[0])}function g(){let e;i.params.pagination=n(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>(0,r.b)(e,".swiper")[0]===i.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=(0,r.m)(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),u=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",p),i.enabled||e.classList.add(t.lockClass)})))}function v(){let e=i.params.pagination;if(h())return;let t=i.pagination.el;t&&(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",p))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}a("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),a("init",()=>{!1===i.params.pagination.enabled?y():(g(),f(),m())}),a("activeIndexChange",()=>{void 0===i.snapIndex&&m()}),a("snapIndexChange",()=>{m()}),a("snapGridLengthChange",()=>{f(),m()}),a("destroy",()=>{v()}),a("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),a("lock unlock",()=>{m()}),a("click",(e,t)=>{let s=t.target,n=(0,r.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&n&&n.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;!0===n[0].classList.contains(i.params.pagination.hiddenClass)?l("paginationShow"):l("paginationHide"),n.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let y=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),v()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),g(),f(),m()},disable:y,render:f,update:m,init:g,destroy:v})}function d(e){let t,i,r,n,a,o,l,d,u,h,{swiper:c,extendParams:p,on:m,emit:f,params:g}=e;c.autoplay={running:!1,paused:!1,timeLeft:0},p({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let v=g&&g.autoplay?g.autoplay.delay:3e3,y=g&&g.autoplay?g.autoplay.delay:3e3,w=new Date().getTime();function b(e){c&&!c.destroyed&&c.wrapperEl&&e.target===c.wrapperEl&&(c.wrapperEl.removeEventListener("transitionend",b),h||e.detail&&e.detail.bySwiperTouchMove||M())}let x=()=>{if(c.destroyed||!c.autoplay.running)return;c.autoplay.paused?n=!0:n&&(y=r,n=!1);let e=c.autoplay.paused?r:w+y-new Date().getTime();c.autoplay.timeLeft=e,f("autoplayTimeLeft",e,e/v),i=requestAnimationFrame(()=>{x()})},S=e=>{if(c.destroyed||!c.autoplay.running)return;cancelAnimationFrame(i),x();let s=void 0===e?c.params.autoplay.delay:e;v=c.params.autoplay.delay,y=c.params.autoplay.delay;let n=(()=>{let e;if(e=c.virtual&&c.params.virtual.enabled?c.slides.find(e=>e.classList.contains("swiper-slide-active")):c.slides[c.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(n)&&n>0&&void 0===e&&(s=n,v=n,y=n),r=s;let a=c.params.speed,o=()=>{c&&!c.destroyed&&(c.params.autoplay.reverseDirection?!c.isBeginning||c.params.loop||c.params.rewind?(c.slidePrev(a,!0,!0),f("autoplay")):c.params.autoplay.stopOnLastSlide||(c.slideTo(c.slides.length-1,a,!0,!0),f("autoplay")):!c.isEnd||c.params.loop||c.params.rewind?(c.slideNext(a,!0,!0),f("autoplay")):c.params.autoplay.stopOnLastSlide||(c.slideTo(0,a,!0,!0),f("autoplay")),c.params.cssMode&&(w=new Date().getTime(),requestAnimationFrame(()=>{S()})))};return s>0?(clearTimeout(t),t=setTimeout(()=>{o()},s)):requestAnimationFrame(()=>{o()}),s},T=()=>{w=new Date().getTime(),c.autoplay.running=!0,S(),f("autoplayStart")},E=()=>{c.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),f("autoplayStop")},P=(e,i)=>{if(c.destroyed||!c.autoplay.running)return;clearTimeout(t),e||(u=!0);let s=()=>{f("autoplayPause"),c.params.autoplay.waitForTransition?c.wrapperEl.addEventListener("transitionend",b):M()};if(c.autoplay.paused=!0,i){d&&(r=c.params.autoplay.delay),d=!1,s();return}r=(r||c.params.autoplay.delay)-(new Date().getTime()-w),c.isEnd&&r<0&&!c.params.loop||(r<0&&(r=0),s())},M=()=>{c.isEnd&&r<0&&!c.params.loop||c.destroyed||!c.autoplay.running||(w=new Date().getTime(),u?(u=!1,S(r)):S(),c.autoplay.paused=!1,f("autoplayResume"))},C=()=>{if(c.destroyed||!c.autoplay.running)return;let e=(0,s.g)();"hidden"===e.visibilityState&&(u=!0,P(!0)),"visible"===e.visibilityState&&M()},A=e=>{"mouse"===e.pointerType&&(u=!0,h=!0,c.animating||c.autoplay.paused||P(!0))},k=e=>{"mouse"===e.pointerType&&(h=!1,c.autoplay.paused&&M())};m("init",()=>{c.params.autoplay.enabled&&(c.params.autoplay.pauseOnMouseEnter&&(c.el.addEventListener("pointerenter",A),c.el.addEventListener("pointerleave",k)),(0,s.g)().addEventListener("visibilitychange",C),T())}),m("destroy",()=>{c.el&&"string"!=typeof c.el&&(c.el.removeEventListener("pointerenter",A),c.el.removeEventListener("pointerleave",k)),(0,s.g)().removeEventListener("visibilitychange",C),c.autoplay.running&&E()}),m("_freeModeStaticRelease",()=>{(o||u)&&M()}),m("_freeModeNoMomentumRelease",()=>{c.params.autoplay.disableOnInteraction?E():P(!0,!0)}),m("beforeTransitionStart",(e,t,i)=>{!c.destroyed&&c.autoplay.running&&(i||!c.params.autoplay.disableOnInteraction?P(!0,!0):E())}),m("sliderFirstMove",()=>{if(!c.destroyed&&c.autoplay.running){if(c.params.autoplay.disableOnInteraction)return void E();a=!0,o=!1,u=!1,l=setTimeout(()=>{u=!0,o=!0,P(!0)},200)}}),m("touchEnd",()=>{if(!c.destroyed&&c.autoplay.running&&a){if(clearTimeout(l),clearTimeout(t),c.params.autoplay.disableOnInteraction){o=!1,a=!1;return}o&&c.params.cssMode&&M(),o=!1,a=!1}}),m("slideChange",()=>{!c.destroyed&&c.autoplay.running&&(d=!0)}),Object.assign(c.autoplay,{start:T,stop:E,pause:P,resume:M})}function u(e){let{swiper:t,extendParams:i,on:n}=e;i({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let a=!1,o=!1;function l(){let e,i=t.thumbs.swiper;if(!i||i.destroyed)return;let s=i.clickedIndex,r=i.clickedSlide;r&&r.classList.contains(t.params.thumbs.slideThumbActiveClass)||null!=s&&(e=i.params.loop?parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10):s,t.params.loop?t.slideToLoop(e):t.slideTo(e))}function d(){let{thumbs:e}=t.params;if(a)return!1;a=!0;let i=t.constructor;if(e.swiper instanceof i){if(e.swiper.destroyed)return a=!1,!1;t.thumbs.swiper=e.swiper,Object.assign(t.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(t.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper.update()}else if((0,r.o)(e.swiper)){let s=Object.assign({},e.swiper);Object.assign(s,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper=new i(s),o=!0}return t.thumbs.swiper.el.classList.add(t.params.thumbs.thumbsContainerClass),t.thumbs.swiper.on("tap",l),!0}function u(e){let i=t.thumbs.swiper;if(!i||i.destroyed)return;let s="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():i.params.slidesPerView,n=1,a=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(n=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(n=1),n=Math.floor(n),i.slides.forEach(e=>e.classList.remove(a)),i.params.loop||i.params.virtual&&i.params.virtual.enabled)for(let e=0;e<n;e+=1)(0,r.e)(i.slidesEl,`[data-swiper-slide-index="${t.realIndex+e}"]`).forEach(e=>{e.classList.add(a)});else for(let e=0;e<n;e+=1)i.slides[t.realIndex+e]&&i.slides[t.realIndex+e].classList.add(a);let o=t.params.thumbs.autoScrollOffset,l=o&&!i.params.loop;if(t.realIndex!==i.realIndex||l){let r,n,a=i.activeIndex;if(i.params.loop){let e=i.slides.find(e=>e.getAttribute("data-swiper-slide-index")===`${t.realIndex}`);r=i.slides.indexOf(e),n=t.activeIndex>t.previousIndex?"next":"prev"}else n=(r=t.realIndex)>t.previousIndex?"next":"prev";l&&(r+="next"===n?o:-1*o),i.visibleSlidesIndexes&&0>i.visibleSlidesIndexes.indexOf(r)&&(i.params.centeredSlides?r=r>a?r-Math.floor(s/2)+1:r+Math.floor(s/2)-1:r>a&&i.params.slidesPerGroup,i.slideTo(r,e?0:void 0))}}t.thumbs={swiper:null},n("beforeInit",()=>{let{thumbs:e}=t.params;if(e&&e.swiper)if("string"==typeof e.swiper||e.swiper instanceof HTMLElement){let i=(0,s.g)(),r=()=>{!t.destroyed&&((()=>{let s="string"==typeof e.swiper?i.querySelector(e.swiper):e.swiper;if(s&&s.swiper)e.swiper=s.swiper,d(),u(!0);else if(s){let i=`${t.params.eventsPrefix}init`,r=n=>{e.swiper=n.detail[0],s.removeEventListener(i,r),d(),u(!0),e.swiper.update(),t.update()};s.addEventListener(i,r)}return s})()||requestAnimationFrame(r))};requestAnimationFrame(r)}else d(),u(!0)}),n("slideChange update resize observerUpdate",()=>{u()}),n("setTransition",(e,i)=>{let s=t.thumbs.swiper;s&&!s.destroyed&&s.setTransition(i)}),n("beforeDestroy",()=>{let e=t.thumbs.swiper;e&&!e.destroyed&&o&&e.destroy()}),Object.assign(t.thumbs,{init:d,update:u})}function h(e,t,i){let s=`swiper-slide-shadow${i?`-${i}`:""}${e?` swiper-slide-shadow-${e}`:""}`,n=(0,r.g)(t),a=n.querySelector(`.${s.split(" ").join(".")}`);return a||(a=(0,r.c)("div",s.split(" ")),n.append(a)),a}function c(e){let t,{swiper:i,extendParams:s,on:n}=e;s({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}});let{effect:a,swiper:o,on:l,setTranslate:d,setTransition:u,overwriteParams:c,perspective:p,recreateShadows:m,getEffectParams:f}={effect:"coverflow",swiper:i,on:n,setTranslate:()=>{let{width:e,height:t,slides:s,slidesSizesGrid:n}=i,a=i.params.coverflowEffect,o=i.isHorizontal(),l=i.translate,d=o?-l+e/2:-l+t/2,u=o?a.rotate:-a.rotate,c=a.depth,p=(0,r.p)(i);for(let e=0,t=s.length;e<t;e+=1){let t=s[e],i=n[e],l=(d-t.swiperSlideOffset-i/2)/i,m="function"==typeof a.modifier?a.modifier(l):l*a.modifier,f=o?u*m:0,g=o?0:u*m,v=-c*Math.abs(m),y=a.stretch;"string"==typeof y&&-1!==y.indexOf("%")&&(y=parseFloat(a.stretch)/100*i);let w=o?0:y*m,b=o?y*m:0,x=1-(1-a.scale)*Math.abs(m);.001>Math.abs(b)&&(b=0),.001>Math.abs(w)&&(w=0),.001>Math.abs(v)&&(v=0),.001>Math.abs(f)&&(f=0),.001>Math.abs(g)&&(g=0),.001>Math.abs(x)&&(x=0);let S=`translate3d(${b}px,${w}px,${v}px)  rotateX(${p(g)}deg) rotateY(${p(f)}deg) scale(${x})`;if(function(e,t){let i=(0,r.g)(t);return i!==t&&(i.style.backfaceVisibility="hidden",i.style["-webkit-backface-visibility"]="hidden"),i}(0,t).style.transform=S,t.style.zIndex=-Math.abs(Math.round(m))+1,a.slideShadows){let e=o?t.querySelector(".swiper-slide-shadow-left"):t.querySelector(".swiper-slide-shadow-top"),i=o?t.querySelector(".swiper-slide-shadow-right"):t.querySelector(".swiper-slide-shadow-bottom");e||(e=h("coverflow",t,o?"left":"top")),i||(i=h("coverflow",t,o?"right":"bottom")),e&&(e.style.opacity=m>0?m:0),i&&(i.style.opacity=-m>0?-m:0)}}},setTransition:e=>{i.slides.map(e=>(0,r.g)(e)).forEach(t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(t=>{t.style.transitionDuration=`${e}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})};l("beforeInit",()=>{if(o.params.effect!==a)return;o.classNames.push(`${o.params.containerModifierClass}${a}`),p&&p()&&o.classNames.push(`${o.params.containerModifierClass}3d`);let e=c?c():{};Object.assign(o.params,e),Object.assign(o.originalParams,e)}),l("setTranslate _virtualUpdated",()=>{o.params.effect===a&&d()}),l("setTransition",(e,t)=>{o.params.effect===a&&u(t)}),l("transitionEnd",()=>{o.params.effect===a&&m&&f&&f().slideShadows&&(o.slides.forEach(e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(e=>e.remove())}),m())}),l("virtualUpdate",()=>{o.params.effect===a&&(o.slides.length||(t=!0),requestAnimationFrame(()=>{t&&o.slides&&o.slides.length&&(d(),t=!1)}))})}},3866:(e,t,i)=>{"use strict";i.d(t,{t:()=>s});let s=(0,i(4232).createContext)(null)},3885:(e,t,i)=>{"use strict";i.d(t,{xQ:()=>n});var s=i(4232),r=i(3866);function n(e=!0){let t=(0,s.useContext)(r.t);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=t,l=(0,s.useId)();(0,s.useEffect)(()=>{if(e)return o(l)},[e]);let d=(0,s.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!i&&a?[!1,d]:[!0]}},4569:(e,t,i)=>{"use strict";i.d(t,{a:()=>d,b:()=>b,c:()=>f,e:()=>c,f:()=>a,g:()=>h,h:()=>x,i:()=>w,k:()=>o,m:()=>S,n:()=>n,o:()=>l,p:()=>T,q:()=>y,r:()=>v,s:()=>E,t:()=>g,u:()=>u,v:()=>m,w:()=>p,x:()=>function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){let r=s<0||arguments.length<=s?void 0:arguments[s];if(null!=r&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(r instanceof HTMLElement):!r||1!==r.nodeType&&11!==r.nodeType)){let s=Object.keys(Object(r)).filter(e=>0>i.indexOf(e));for(let i=0,n=s.length;i<n;i+=1){let n=s[i],a=Object.getOwnPropertyDescriptor(r,n);void 0!==a&&a.enumerable&&(l(t[n])&&l(r[n])?r[n].__swiper__?t[n]=r[n]:e(t[n],r[n]):!l(t[n])&&l(r[n])?(t[n]={},r[n].__swiper__?t[n]=r[n]:e(t[n],r[n])):t[n]=r[n])}}}return t},y:()=>r});var s=i(2370);function r(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function n(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function a(){return Date.now()}function o(e,t){let i,r,n;void 0===t&&(t="x");let a=(0,s.a)(),o=function(e){let t,i=(0,s.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((r=o.transform||o.webkitTransform).split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),n=new a.WebKitCSSMatrix("none"===r?"":r)):i=(n=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=a.WebKitCSSMatrix?n.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=a.WebKitCSSMatrix?n.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function l(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e,t,i){e.style.setProperty(t,i)}function u(e){let t,{swiper:i,targetPosition:r,side:n}=e,a=(0,s.a)(),o=-i.translate,l=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let u=r>o?"next":"prev",h=(e,t)=>"next"===u&&e>=t||"prev"===u&&e<=t,c=()=>{t=new Date().getTime(),null===l&&(l=t);let e=o+(.5-Math.cos(Math.max(Math.min((t-l)/d,1),0)*Math.PI)/2)*(r-o);if(h(e,r)&&(e=r),i.wrapperEl.scrollTo({[n]:e}),h(e,r)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[n]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(c)};c()}function h(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function c(e,t){void 0===t&&(t="");let i=(0,s.a)(),r=[...e.children];return(i.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t)?r.filter(e=>e.matches(t)):r}function p(e,t){let i=(0,s.a)(),r=t.contains(e);return!r&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&((r=[...t.assignedElements()].includes(e))||(r=function(e,t){let i=[t];for(;i.length>0;){let t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),r}function m(e){try{console.warn(e);return}catch(e){}}function f(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function g(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function v(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function y(e,t){return(0,s.a)().getComputedStyle(e,null).getPropertyValue(t)}function w(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function b(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function x(e,t,i){let r=(0,s.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function S(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function T(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}function E(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}},5048:(e,t,i)=>{"use strict";i.d(t,{L:()=>s});let s=(0,i(4232).createContext)({})},6667:()=>{},7212:(e,t,i)=>{"use strict";i.d(t,{N:()=>y});var s=i(7876),r=i(4232),n=i(5048),a=i(1200),o=i(181),l=i(3866),d=i(7990),u=i(9751);class h extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,d.s)(e)&&e.offsetWidth||0,s=this.props.sizeRef.current;s.height=t.offsetHeight||0,s.width=t.offsetWidth||0,s.top=t.offsetTop,s.left=t.offsetLeft,s.right=i-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t,anchorX:i,root:n}){let a=(0,r.useId)(),o=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,r.useContext)(u.Q);return(0,r.useInsertionEffect)(()=>{let{width:e,height:s,top:r,left:u,right:h}=l.current;if(t||!o.current||!e||!s)return;let c="left"===i?`left: ${u}`:`right: ${h}`;o.current.dataset.motionPopId=a;let p=document.createElement("style");d&&(p.nonce=d);let m=n??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${c}px !important;
            top: ${r}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[t]),(0,s.jsx)(h,{isPresent:t,childRef:o,sizeRef:l,children:r.cloneElement(e,{ref:o})})}let p=({children:e,initial:t,isPresent:i,onExitComplete:n,custom:o,presenceAffectsLayout:d,mode:u,anchorX:h,root:p})=>{let f=(0,a.M)(m),g=(0,r.useId)(),v=!0,y=(0,r.useMemo)(()=>(v=!1,{id:g,initial:t,isPresent:i,custom:o,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;n&&n()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[i,f,n]);return d&&v&&(y={...y}),(0,r.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[i]),r.useEffect(()=>{i||f.size||!n||n()},[i]),"popLayout"===u&&(e=(0,s.jsx)(c,{isPresent:i,anchorX:h,root:p,children:e})),(0,s.jsx)(l.t.Provider,{value:y,children:e})};function m(){return new Map}var f=i(3885);let g=e=>e.key||"";function v(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:i=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:u="sync",propagate:h=!1,anchorX:c="left",root:m})=>{let[y,w]=(0,f.xQ)(h),b=(0,r.useMemo)(()=>v(e),[e]),x=h&&!y?[]:b.map(g),S=(0,r.useRef)(!0),T=(0,r.useRef)(b),E=(0,a.M)(()=>new Map),[P,M]=(0,r.useState)(b),[C,A]=(0,r.useState)(b);(0,o.E)(()=>{S.current=!1,T.current=b;for(let e=0;e<C.length;e++){let t=g(C[e]);x.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[C,x.length,x.join("-")]);let k=[];if(b!==P){let e=[...b];for(let t=0;t<C.length;t++){let i=C[t],s=g(i);x.includes(s)||(e.splice(t,0,i),k.push(i))}return"wait"===u&&k.length&&(e=k),A(v(e)),M(b),null}let{forceRender:L}=(0,r.useContext)(n.L);return(0,s.jsx)(s.Fragment,{children:C.map(e=>{let r=g(e),n=(!h||!!y)&&(b===C||x.includes(r));return(0,s.jsx)(p,{isPresent:n,initial:(!S.current||!!i)&&void 0,custom:t,presenceAffectsLayout:d,mode:u,root:m,onExitComplete:n?void 0:()=>{if(!E.has(r))return;E.set(r,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(L?.(),A(T.current),h&&w?.(),l&&l())},anchorX:c,children:e},r)})})}},7328:(e,t,i)=>{e.exports=i(9836)},7594:(e,t,i)=>{"use strict";function s(e){return"object"==typeof e&&null!==e}i.d(t,{G:()=>s})},7888:(e,t,i)=>{"use strict";let s,r,n;i.d(t,{RC:()=>W,qr:()=>U});var a=i(4232),o=i(2370),l=i(4569);function d(){return s||(s=function(){let e=(0,o.a)(),t=(0,o.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function u(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=d(),s=(0,o.a)(),r=s.navigator.platform,n=t||s.navigator.userAgent,a={ios:!1,android:!1},l=s.screen.width,u=s.screen.height,h=n.match(/(Android);?[\s\/]+([\d.]+)?/),c=n.match(/(iPad).*OS\s([\d_]+)/),p=n.match(/(iPod)(.*OS\s([\d_]+))?/),m=!c&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="MacIntel"===r;return!c&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${u}`)>=0&&((c=n.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),f=!1),h&&"Win32"!==r&&(a.os="android",a.android=!0),(c||m||p)&&(a.os="ios",a.ios=!0),a}(e)),r}function h(){return n||(n=function(){let e=(0,o.a)(),t=u(),i=!1;function s(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(s()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),n=s(),a=n||r&&t.ios;return{isSafari:i||n,needPerspectiveFix:i,need3dFix:a,isWebView:r}}()),n}let c=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},p=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},m=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},f=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},g=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&f(e,s)});return}let n=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=n+t;s+=1){let t=(s%i+i)%i;(t<r||t>n)&&f(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(n+t,i-1);s+=1)s!==r&&(s>n||s<r)&&f(e,s)};function v(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:n,previousIndex:a}=t,o=s;o||(o=n>a?"next":n<a?"prev":"reset"),t.emit(`transition${r}`),i&&"reset"===o?t.emit(`slideResetTransition${r}`):i&&n!==a&&(t.emit(`slideChangeTransition${r}`),"next"===o?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function y(e,t,i){let s=(0,o.a)(),{params:r}=e,n=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return!n||!(i<=a)&&!(i>=s.innerWidth-a)||"prevent"===n&&(t.preventDefault(),!0)}function w(e){let t=(0,o.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type)return void y(this,i,i.targetTouches[0].pageX);let{params:r,touches:n,enabled:a}=this;if(!a||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let d=i.target;if("wrapper"===r.touchEventsTarget&&!(0,l.w)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let u=!!r.noSwipingClass&&""!==r.noSwipingClass,h=i.composedPath?i.composedPath():i.path;u&&i.target&&i.target.shadowRoot&&h&&(d=h[0]);let c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,p=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(p?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,o.g)()||i===(0,o.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(c,d):d.closest(c))){this.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;n.currentX=i.pageX,n.currentY=i.pageY;let m=n.currentX,f=n.currentY;if(!y(this,i,m))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),n.startX=m,n.startY=f,s.touchStartTime=(0,l.f)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let g=!0;d.matches(s.focusableElements)&&(g=!1,"SELECT"===d.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(s.focusableElements))&&t.activeElement.blur();let v=g&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||v)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function b(e){let t,i,s=(0,o.g)(),r=this.touchEventsData,{params:n,touches:a,rtlTranslate:d,enabled:u}=this;if(!u||!n.simulateTouch&&"mouse"===e.pointerType)return;let h=e;if(h.originalEvent&&(h=h.originalEvent),"pointermove"===h.type&&(null!==r.touchId||h.pointerId!==r.pointerId))return;if("touchmove"===h.type){if(!(t=[...h.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else t=h;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",h);return}let c=t.pageX,p=t.pageY;if(h.preventedByNestedSwiper){a.startX=c,a.startY=p;return}if(!this.allowTouchMove){h.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p}),r.touchStartTime=(0,l.f)());return}if(n.touchReleaseOnEdges&&!n.loop){if(this.isVertical()){if(p<a.startY&&this.translate<=this.maxTranslate()||p>a.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(d&&(c>a.startX&&-this.translate<=this.maxTranslate()||c<a.startX&&-this.translate>=this.minTranslate()))return;else if(!d&&(c<a.startX&&this.translate<=this.maxTranslate()||c>a.startX&&this.translate>=this.minTranslate()))return}if(s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==h.target&&"mouse"!==h.pointerType&&s.activeElement.blur(),s.activeElement&&h.target===s.activeElement&&h.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",h),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=c,a.currentY=p;let m=a.currentX-a.startX,f=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(m**2+f**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?r.isScrolling=!1:m*m+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(m))/Math.PI,r.isScrolling=this.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",h),void 0===r.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===h.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!n.cssMode&&h.cancelable&&h.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&h.stopPropagation();let g=this.isHorizontal()?m:f,v=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;n.oneWayMovement&&(g=Math.abs(g)*(d?1:-1),v=Math.abs(v)*(d?1:-1)),a.diff=g,g*=n.touchRatio,d&&(g=-g,v=-v);let y=this.touchesDirection;this.swipeDirection=g>0?"prev":"next",this.touchesDirection=v>0?"prev":"next";let w=this.params.loop&&!n.cssMode,b="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(w&&b&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,n.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",h)}if(new Date().getTime(),!1!==n._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&y!==this.touchesDirection&&w&&b&&Math.abs(g)>=1){Object.assign(a,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",h),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let x=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),g>0?(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate>(n.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==n.slidesPerView&&this.slides.length-n.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(x=!1,n.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+g)**S))):g<0&&(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate<(n.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==n.slidesPerView&&this.slides.length-n.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===n.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(x=!1,n.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-g)**S))),x&&(h.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),n.threshold>0)if(Math.abs(g)>n.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,r.currentTranslate=r.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{r.currentTranslate=r.startTranslate;return}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&this.freeMode||n.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function x(e){let t,i,s=this,r=s.touchEventsData,n=e;if(n.originalEvent&&(n=n.originalEvent),"touchend"===n.type||"touchcancel"===n.type){if(!(t=[...n.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else{if(null!==r.touchId||n.pointerId!==r.pointerId)return;t=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&!(["pointercancel","contextmenu"].includes(n.type)&&(s.browser.isSafari||s.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:a,touches:o,rtlTranslate:d,slidesGrid:u,enabled:h}=s;if(!h||!a.simulateTouch&&"mouse"===n.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",n),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&a.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}a.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let c=(0,l.f)(),p=c-r.touchStartTime;if(s.allowClick){let e=n.path||n.composedPath&&n.composedPath();s.updateClickedSlide(e&&e[0]||n.target,e),s.emit("tap click",n),p<300&&c-r.lastClickTime<300&&s.emit("doubleTap doubleClick",n)}if(r.lastClickTime=(0,l.f)(),(0,l.n)(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===o.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=a.followFinger?d?s.translate:-s.translate:-r.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled)return void s.freeMode.onTouchEnd({currentPos:i});let m=i>=-s.maxTranslate()&&!s.params.loop,f=0,g=s.slidesSizesGrid[0];for(let e=0;e<u.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){let t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==u[e+t]?(m||i>=u[e]&&i<u[e+t])&&(f=e,g=u[e+t]-u[e]):(m||i>=u[e])&&(f=e,g=u[u.length-1]-u[u.length-2])}let v=null,y=null;a.rewind&&(s.isBeginning?y=a.virtual&&a.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(v=0));let w=(i-u[f])/g,b=f<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(p>a.longSwipesMs){if(!a.longSwipes)return void s.slideTo(s.activeIndex);"next"===s.swipeDirection&&(w>=a.longSwipesRatio?s.slideTo(a.rewind&&s.isEnd?v:f+b):s.slideTo(f)),"prev"===s.swipeDirection&&(w>1-a.longSwipesRatio?s.slideTo(f+b):null!==y&&w<0&&Math.abs(w)>a.longSwipesRatio?s.slideTo(y):s.slideTo(f))}else{if(!a.shortSwipes)return void s.slideTo(s.activeIndex);s.navigation&&(n.target===s.navigation.nextEl||n.target===s.navigation.prevEl)?n.target===s.navigation.nextEl?s.slideTo(f+b):s.slideTo(f):("next"===s.swipeDirection&&s.slideTo(null!==v?v:f+b),"prev"===s.swipeDirection&&s.slideTo(null!==y?y:f))}}function S(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:n}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let o=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&n!==e.snapGrid&&e.checkOverflow()}function T(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function E(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function P(e){m(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function M(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let C=(e,t)=>{let i=(0,o.g)(),{params:s,el:r,wrapperEl:n,device:a}=e,l=!!s.nested,d="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:l}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:l}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",e.onClick,!0),s.cssMode&&n[d]("scroll",e.onScroll),s.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",S,!0):e[t]("observerUpdate",S,!0),r[d]("load",e.onLoad,{capture:!0}))},A=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var k={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let L={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,n=Array(i),a=0;a<i;a++)n[a]=arguments[a];t.apply(s,n)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i,s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return"string"==typeof n[0]||Array.isArray(n[0])?(e=n[0],t=n.slice(1,n.length),i=s):(e=n[0].events,t=n[0].data,i=n[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t,i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,l.q)(i,"padding-left")||0,10)-parseInt((0,l.q)(i,"padding-right")||0,10),t=t-parseInt((0,l.q)(i,"padding-top")||0,10)-parseInt((0,l.q)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e,t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:r,slidesEl:n,size:a,rtlTranslate:o,wrongRTL:d}=t,u=t.virtual&&s.virtual.enabled,h=u?t.virtual.slides.length:t.slides.length,c=(0,l.e)(n,`.${t.params.slideClass}, swiper-slide`),p=u?t.virtual.slides.length:c.length,m=[],f=[],g=[],v=s.slidesOffsetBefore;"function"==typeof v&&(v=s.slidesOffsetBefore.call(t));let y=s.slidesOffsetAfter;"function"==typeof y&&(y=s.slidesOffsetAfter.call(t));let w=t.snapGrid.length,b=t.slidesGrid.length,x=s.spaceBetween,S=-v,T=0,E=0;if(void 0===a)return;"string"==typeof x&&x.indexOf("%")>=0?x=parseFloat(x.replace("%",""))/100*a:"string"==typeof x&&(x=parseFloat(x)),t.virtualSize=-x,c.forEach(e=>{o?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&((0,l.a)(r,"--swiper-centered-offset-before",""),(0,l.a)(r,"--swiper-centered-offset-after",""));let P=s.grid&&s.grid.rows>1&&t.grid;P?t.grid.initSlides(c):t.grid&&t.grid.unsetSlides();let M="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let r=0;r<p;r+=1){let n;if(e=0,c[r]&&(n=c[r]),P&&t.grid.updateSlide(r,n,c),!c[r]||"none"!==(0,l.q)(n,"display")){if("auto"===s.slidesPerView){M&&(c[r].style[t.getDirectionLabel("width")]="");let a=getComputedStyle(n),o=n.style.transform,d=n.style.webkitTransform;if(o&&(n.style.transform="none"),d&&(n.style.webkitTransform="none"),s.roundLengths)e=t.isHorizontal()?(0,l.h)(n,"width",!0):(0,l.h)(n,"height",!0);else{let t=i(a,"width"),s=i(a,"padding-left"),r=i(a,"padding-right"),o=i(a,"margin-left"),l=i(a,"margin-right"),d=a.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+o+l;else{let{clientWidth:i,offsetWidth:a}=n;e=t+s+r+o+l+(a-i)}}o&&(n.style.transform=o),d&&(n.style.webkitTransform=d),s.roundLengths&&(e=Math.floor(e))}else e=(a-(s.slidesPerView-1)*x)/s.slidesPerView,s.roundLengths&&(e=Math.floor(e)),c[r]&&(c[r].style[t.getDirectionLabel("width")]=`${e}px`);c[r]&&(c[r].swiperSlideSize=e),g.push(e),s.centeredSlides?(S=S+e/2+T/2+x,0===T&&0!==r&&(S=S-a/2-x),0===r&&(S=S-a/2-x),.001>Math.abs(S)&&(S=0),s.roundLengths&&(S=Math.floor(S)),E%s.slidesPerGroup==0&&m.push(S),f.push(S)):(s.roundLengths&&(S=Math.floor(S)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup==0&&m.push(S),f.push(S),S=S+e+x),t.virtualSize+=e+x,T=e,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+y,o&&d&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${t.virtualSize+x}px`),s.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+x}px`),P&&t.grid.updateWrapperSize(e,m),!s.centeredSlides){let e=[];for(let i=0;i<m.length;i+=1){let r=m[i];s.roundLengths&&(r=Math.floor(r)),m[i]<=t.virtualSize-a&&e.push(r)}m=e,Math.floor(t.virtualSize-a)-Math.floor(m[m.length-1])>1&&m.push(t.virtualSize-a)}if(u&&s.loop){let e=g[0]+x;if(s.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),r=e*s.slidesPerGroup;for(let e=0;e<i;e+=1)m.push(m[m.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&m.push(m[m.length-1]+e),f.push(f[f.length-1]+e),t.virtualSize+=e}if(0===m.length&&(m=[0]),0!==x){let e=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");c.filter((e,t)=>!s.cssMode||!!s.loop||t!==c.length-1).forEach(t=>{t.style[e]=`${x}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;g.forEach(t=>{e+=t+(x||0)});let t=(e-=x)>a?e-a:0;m=m.map(e=>e<=0?-v:e>t?t+y:e)}if(s.centerInsufficientSlides){let e=0;g.forEach(t=>{e+=t+(x||0)}),e-=x;let t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<a){let i=(a-e-t)/2;m.forEach((e,t)=>{m[t]=e-i}),f.forEach((e,t)=>{f[t]=e+i})}}if(Object.assign(t,{slides:c,snapGrid:m,slidesGrid:f,slidesSizesGrid:g}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){(0,l.a)(r,"--swiper-centered-offset-before",`${-m[0]}px`),(0,l.a)(r,"--swiper-centered-offset-after",`${t.size/2-g[g.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(p!==h&&t.emit("slidesLengthChange"),m.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),f.length!==b&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!u&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);p<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t,i=this,s=[],r=i.virtual&&i.params.virtual.enabled,n=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let a=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1)if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(a(e))}else s.push(a(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;n=e>n?e:n}(n||0===n)&&(i.wrapperEl.style.height=`${n}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let n=-e;s&&(n=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let o=i[e],l=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(l-=i[0].swiperSlideOffset);let d=(n+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),u=(n-r[0]+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),h=-(n-l),p=h+this.slidesSizesGrid[e],m=h>=0&&h<=this.size-this.slidesSizesGrid[e],f=h>=0&&h<this.size-1||p>1&&p<=this.size||h<=0&&p>=this.size;f&&(this.visibleSlides.push(o),this.visibleSlidesIndexes.push(e)),c(o,f,t.slideVisibleClass),c(o,m,t.slideFullyVisibleClass),o.progress=s?-d:d,o.originalProgress=s?-u:u}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:n,progressLoop:a}=this,o=r,l=n;if(0===i)s=0,r=!0,n=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());r=t||s<=0,n=a||s>=1,t&&(s=0),a&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],n=this.slidesGrid[this.slidesGrid.length-1],o=Math.abs(e);(a=o>=s?(o-s)/n:(o+n-r)/n)>1&&(a-=1)}Object.assign(this,{progress:s,progressLoop:a,isBeginning:r,isEnd:n}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!o&&this.emit("reachBeginning toEdge"),n&&!l&&this.emit("reachEnd toEdge"),(o&&!r||l&&!n)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i,{slides:s,params:r,slidesEl:n,activeIndex:a}=this,o=this.virtual&&r.virtual.enabled,d=this.grid&&r.grid&&r.grid.rows>1,u=e=>(0,l.e)(n,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(o)if(r.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=u(`[data-swiper-slide-index="${t}"]`)}else e=u(`[data-swiper-slide-index="${a}"]`);else d?(e=s.find(e=>e.column===a),i=s.find(e=>e.column===a+1),t=s.find(e=>e.column===a-1)):e=s[a];e&&!d&&(i=(0,l.r)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=(0,l.t)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{p(s,s===e,r.slideActiveClass),p(s,s===i,r.slideNextClass),p(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i,s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:n,params:a,activeIndex:o,realIndex:l,snapIndex:d}=s,u=e,h=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===u&&(u=function(e){let t,{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),n.indexOf(r)>=0)t=n.indexOf(r);else{let e=Math.min(a.slidesPerGroupSkip,u);t=e+Math.floor((u-e)/a.slidesPerGroup)}if(t>=n.length&&(t=n.length-1),u===o&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(u===o&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=h(u);return}let c=s.grid&&a.grid&&a.grid.rows>1;if(s.virtual&&a.virtual.enabled&&a.loop)i=h(u);else if(c){let e=s.slides.find(e=>e.column===u),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/a.grid.rows)}else if(s.slides[u]){let e=s.slides[u].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):u}else i=u;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:l,realIndex:i,previousIndex:o,activeIndex:u}),s.initialized&&g(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(l!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i,s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let n=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){n=!0,i=e;break}}if(r&&n)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let n=(0,l.k)(r,e);return n+=this.cssOverflowAdjustment(),i&&(n=-n),n||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:n}=this,a=0,o=0;this.isHorizontal()?a=i?-e:e:o=e,s.roundLengths&&(a=Math.floor(a),o=Math.floor(o)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:o,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-o:s.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():o-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${o}px, 0px)`);let l=this.maxTranslate()-this.minTranslate();(0===l?0:(e-this.minTranslate())/l)!==n&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let n;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let a=this,{params:o,wrapperEl:d}=a;if(a.animating&&o.preventInteractionOnTransition)return!1;let u=a.minTranslate(),h=a.maxTranslate();if(n=s&&e>u?u:s&&e<h?h:e,a.updateProgress(n),o.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-n;else{if(!a.support.smoothScroll)return(0,l.u)({swiper:a,targetPosition:-n,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-n,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(n),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),v({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),v({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let n;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,o=e;o<0&&(o=0);let{params:d,snapGrid:u,slidesGrid:c,previousIndex:p,activeIndex:m,rtlTranslate:f,wrapperEl:g,enabled:v}=a;if(!v&&!s&&!r||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let y=Math.min(a.params.slidesPerGroupSkip,o),w=y+Math.floor((o-y)/a.params.slidesPerGroup);w>=u.length&&(w=u.length-1);let b=-u[w];if(d.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){let t=-Math.floor(100*b),i=Math.floor(100*c[e]),s=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=i&&t<s-(s-i)/2?o=e:t>=i&&t<s&&(o=e+1):t>=i&&(o=e)}if(a.initialized&&o!==m&&(!a.allowSlideNext&&(f?b>a.translate&&b>a.minTranslate():b<a.translate&&b<a.minTranslate())||!a.allowSlidePrev&&b>a.translate&&b>a.maxTranslate()&&(m||0)!==o))return!1;o!==(p||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(b),n=o>m?"next":o<m?"prev":"reset";let x=a.virtual&&a.params.virtual.enabled;if(!(x&&r)&&(f&&-b===a.translate||!f&&b===a.translate))return a.updateActiveIndex(o),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(b),"reset"!==n&&(a.transitionStart(i,n),a.transitionEnd(i,n)),!1;if(d.cssMode){let e=a.isHorizontal(),i=f?b:-b;if(0===t)x&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),x&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[e?"scrollLeft":"scrollTop"]=i})):g[e?"scrollLeft":"scrollTop"]=i,x&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return(0,l.u)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;g.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}let S=h().isSafari;return x&&!r&&S&&a.isElement&&a.virtual.update(!1,!1,o),a.setTransition(t),a.setTranslate(b),a.updateActiveIndex(o),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,s),a.transitionStart(i,n),0===t?a.transitionEnd(i,n):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,n))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let n=r.grid&&r.params.grid&&r.params.grid.rows>1,a=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)a+=r.virtual.slidesBefore;else{let e;if(n){let t=a*r.params.grid.rows;e=r.slides.find(e=>+e.getAttribute("data-swiper-slide-index")===t).column}else e=r.getSlideIndexByData(a);let t=n?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,o=r.params.slidesPerView;"auto"===o?o=r.slidesPerViewDynamic():(o=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&o%2==0&&(o+=1));let l=t-e<o;if(i&&(l=l||e<Math.ceil(o/2)),s&&i&&"auto"!==r.params.slidesPerView&&!n&&(l=!1),l){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(n){let e=a*r.params.grid.rows;a=r.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e).column}else a=r.getSlideIndexByData(a)}return requestAnimationFrame(()=>{r.slideTo(a,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:n,animating:a}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let o=n.slidesPerGroup;"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));let l=s.activeIndex<n.slidesPerGroupSkip?1:o,d=s.virtual&&n.virtual.enabled;if(n.loop){if(a&&!d&&n.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&n.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+l,e,t,i)}),!0}return n.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:n,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=s;if(!l||s.destroyed)return s;void 0===e&&(e=s.params.speed);let u=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!u&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function h(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let c=h(o?s.translate:-s.translate),p=n.map(e=>h(e)),m=r.freeMode&&r.freeMode.enabled,f=n[p.indexOf(c)-1];if(void 0===f&&(r.cssMode||m)){let e;n.forEach((t,i)=>{c>=t&&(e=i)}),void 0!==e&&(f=m?n[e]:n[e>0?e-1:e])}let g=0;if(void 0!==f&&((g=a.indexOf(f))<0&&(g=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(g=Math.max(g=g-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(g,e,t,i)}),!0):s.slideTo(g,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,n=Math.min(this.params.slidesPerGroupSkip,r),a=n+Math.floor((r-n)/this.params.slidesPerGroup),o=this.rtlTranslate?this.translate:-this.translate;if(o>=this.snapGrid[a]){let e=this.snapGrid[a];o-e>(this.snapGrid[a+1]-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];o-e<=(this.snapGrid[a]-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e,t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,n=t.getSlideIndexWhenGrid(t.clickedIndex),a=t.isElement?"swiper-slide":`.${i.slideClass}`,o=t.grid&&t.params.grid&&t.params.grid.rows>1;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?t.slideToLoop(e):n>(o?(t.slides.length-r)/2-(t.params.grid.rows-1):t.slides.length-r)?(t.loopFix(),n=t.getSlideIndex((0,l.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,l.n)(()=>{t.slideTo(n)})):t.slideTo(n)}else t.slideTo(n)}},loop:{loopCreate:function(e,t){let i=this,{params:s,slidesEl:r}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;let n=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||n)&&(()=>{let e=(0,l.e)(r,`.${s.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();let a=s.slidesPerGroup*(n?s.grid.rows:1),o=i.slides.length%a!=0,d=n&&i.slides.length%s.grid.rows!=0,u=e=>{for(let t=0;t<e;t+=1){let e=i.isElement?(0,l.c)("swiper-slide",[s.slideBlankClass]):(0,l.c)("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(e)}};o?s.loopAddBlankSlides?(u(a-i.slides.length%a),i.recalcSlides(),i.updateSlides()):(0,l.v)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):d&&(s.loopAddBlankSlides?(u(s.grid.rows-i.slides.length%s.grid.rows),i.recalcSlides(),i.updateSlides()):(0,l.v)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,l.e)(r,`.${s.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),i.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:n,initial:a,byController:o,byMousewheel:d}=void 0===e?{}:e,u=this;if(!u.params.loop)return;u.emit("beforeLoopFix");let{slides:h,allowSlidePrev:c,allowSlideNext:p,slidesEl:m,params:f}=u,{centeredSlides:g,initialSlide:v}=f;if(u.allowSlidePrev=!0,u.allowSlideNext=!0,u.virtual&&f.virtual.enabled){i&&(f.centeredSlides||0!==u.snapIndex?f.centeredSlides&&u.snapIndex<f.slidesPerView?u.slideTo(u.virtual.slides.length+u.snapIndex,0,!1,!0):u.snapIndex===u.snapGrid.length-1&&u.slideTo(u.virtual.slidesBefore,0,!1,!0):u.slideTo(u.virtual.slides.length,0,!1,!0)),u.allowSlidePrev=c,u.allowSlideNext=p,u.emit("loopFix");return}let y=f.slidesPerView;"auto"===y?y=u.slidesPerViewDynamic():(y=Math.ceil(parseFloat(f.slidesPerView,10)),g&&y%2==0&&(y+=1));let w=f.slidesPerGroupAuto?y:f.slidesPerGroup,b=g?Math.max(w,Math.ceil(y/2)):w;b%w!=0&&(b+=w-b%w),u.loopedSlides=b+=f.loopAdditionalSlides;let x=u.grid&&f.grid&&f.grid.rows>1;h.length<y+b||"cards"===u.params.effect&&h.length<y+2*b?(0,l.v)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&"row"===f.grid.fill&&(0,l.v)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let S=[],T=[],E=x?Math.ceil(h.length/f.grid.rows):h.length,P=a&&E-v<y&&!g,M=P?v:u.activeIndex;void 0===n?n=u.getSlideIndex(h.find(e=>e.classList.contains(f.slideActiveClass))):M=n;let C="next"===s||!s,A="prev"===s||!s,k=0,L=0,O=(x?h[n].column:n)+(g&&void 0===r?-y/2+.5:0);if(O<b){k=Math.max(b-O,w);for(let e=0;e<b-O;e+=1){let t=e-Math.floor(e/E)*E;if(x){let e=E-t-1;for(let t=h.length-1;t>=0;t-=1)h[t].column===e&&S.push(t)}else S.push(E-t-1)}}else if(O+y>E-b){L=Math.max(O-(E-2*b),w),P&&(L=Math.max(L,y-E+v+1));for(let e=0;e<L;e+=1){let t=e-Math.floor(e/E)*E;x?h.forEach((e,i)=>{e.column===t&&T.push(i)}):T.push(t)}}if(u.__preventObserver__=!0,requestAnimationFrame(()=>{u.__preventObserver__=!1}),"cards"===u.params.effect&&h.length<y+2*b&&(T.includes(n)&&T.splice(T.indexOf(n),1),S.includes(n)&&S.splice(S.indexOf(n),1)),A&&S.forEach(e=>{h[e].swiperLoopMoveDOM=!0,m.prepend(h[e]),h[e].swiperLoopMoveDOM=!1}),C&&T.forEach(e=>{h[e].swiperLoopMoveDOM=!0,m.append(h[e]),h[e].swiperLoopMoveDOM=!1}),u.recalcSlides(),"auto"===f.slidesPerView?u.updateSlides():x&&(S.length>0&&A||T.length>0&&C)&&u.slides.forEach((e,t)=>{u.grid.updateSlide(t,e,u.slides)}),f.watchSlidesProgress&&u.updateSlidesOffset(),i){if(S.length>0&&A){if(void 0===t){let e=u.slidesGrid[M],t=u.slidesGrid[M+k]-e;d?u.setTranslate(u.translate-t):(u.slideTo(M+Math.ceil(k),0,!1,!0),r&&(u.touchEventsData.startTranslate=u.touchEventsData.startTranslate-t,u.touchEventsData.currentTranslate=u.touchEventsData.currentTranslate-t))}else if(r){let e=x?S.length/f.grid.rows:S.length;u.slideTo(u.activeIndex+e,0,!1,!0),u.touchEventsData.currentTranslate=u.translate}}else if(T.length>0&&C)if(void 0===t){let e=u.slidesGrid[M],t=u.slidesGrid[M-L]-e;d?u.setTranslate(u.translate-t):(u.slideTo(M-L,0,!1,!0),r&&(u.touchEventsData.startTranslate=u.touchEventsData.startTranslate-t,u.touchEventsData.currentTranslate=u.touchEventsData.currentTranslate-t))}else{let e=x?T.length/f.grid.rows:T.length;u.slideTo(u.activeIndex-e,0,!1,!0)}}if(u.allowSlidePrev=c,u.allowSlideNext=p,u.controller&&u.controller.control&&!o){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:n,byController:!0};Array.isArray(u.controller.control)?u.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===f.slidesPerView&&i})}):u.controller.control instanceof u.constructor&&u.controller.control.params.loop&&u.controller.control.loopFix({...e,slideTo:u.controller.control.params.slidesPerView===f.slidesPerView&&i})}u.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=w.bind(this),this.onTouchMove=b.bind(this),this.onTouchEnd=x.bind(this),this.onDocumentTouchStart=M.bind(this),e.cssMode&&(this.onScroll=E.bind(this)),this.onClick=T.bind(this),this.onLoad=P.bind(this),C(this,"on")},detachEvents:function(){C(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,n=s.breakpoints;if(!n||n&&0===Object.keys(n).length)return;let a=(0,o.g)(),d="window"!==s.breakpointsBase&&s.breakpointsBase?"container":s.breakpointsBase,u=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:a.querySelector(s.breakpointsBase),h=e.getBreakpoint(n,d,u);if(!h||e.currentBreakpoint===h)return;let c=(h in n?n[h]:void 0)||e.originalParams,p=A(e,s),m=A(e,c),f=e.params.grabCursor,g=c.grabCursor,v=s.enabled;p&&!m?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&m&&(r.classList.add(`${s.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),f&&!g?e.unsetGrabCursor():!f&&g&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===c[t])return;let i=s[t]&&s[t].enabled,r=c[t]&&c[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let y=c.direction&&c.direction!==s.direction,w=s.loop&&(c.slidesPerView!==s.slidesPerView||y),b=s.loop;y&&i&&e.changeDirection(),(0,l.x)(e.params,c);let x=e.params.enabled,S=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!x?e.disable():!v&&x&&e.enable(),e.currentBreakpoint=h,e.emit("_beforeBreakpoint",c),i&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!b&&S?(e.loopCreate(t),e.updateSlides()):b&&!S&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=(0,o.a)(),n="window"===t?r.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:n*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:n,value:o}=a[e];"window"===t?r.matchMedia(`(min-width: ${o}px)`).matches&&(s=n):o<=i.clientWidth&&(s=n)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,n=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...n),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},O={};class D{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=(0,l.x)({},t),e&&!t.el&&(t.el=e);let n=(0,o.g)();if(t.el&&"string"==typeof t.el&&n.querySelectorAll(t.el).length>1){let e=[];return n.querySelectorAll(t.el).forEach(i=>{let s=(0,l.x)({},t,{el:i});e.push(new D(s))}),e}let a=this;a.__swiper__=!0,a.support=d(),a.device=u({userAgent:t.userAgent}),a.browser=h(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);let c={};a.modules.forEach(e=>{e({params:t,swiper:a,extendParams:function(e,t){return function(i){void 0===i&&(i={});let s=Object.keys(i)[0],r=i[s];return"object"!=typeof r||null===r?void(0,l.x)(t,i):(!0===e[s]&&(e[s]={enabled:!0}),"navigation"===s&&e[s]&&e[s].enabled&&!e[s].prevEl&&!e[s].nextEl&&(e[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&e[s]&&e[s].enabled&&!e[s].el&&(e[s].auto=!0),s in e&&"enabled"in r)?void("object"==typeof e[s]&&!("enabled"in e[s])&&(e[s].enabled=!0),!e[s]&&(e[s]={enabled:!1}),(0,l.x)(t,i)):void(0,l.x)(t,i)}}(t,c),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});let p=(0,l.x)({},k,c);return a.params=(0,l.x)({},p,O,t),a.originalParams=(0,l.x)({},a.params),a.passedParams=(0,l.x)({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=(0,l.e)(t,`.${i.slideClass}, swiper-slide`),r=(0,l.i)(s[0]);return(0,l.i)(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,l.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:n,size:a,activeIndex:o}=this,l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[o]?Math.ceil(s[o].swiperSlideSize):0;for(let i=o+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),l+=1,t>a&&(e=!0));for(let i=o-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<s.length;e+=1)(t?r[e]+n[e]-r[o]<a:r[e]-r[o]<a)&&(l+=1);else for(let e=o-1;e>=0;e-=1)r[o]-r[e]<a&&(l+=1);return l}update(){let e,t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&m(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):(0,l.e)(i,s())[0];return!r&&t.params.createElements&&(r=(0,l.c)("div",t.params.wrapperClass),i.append(r),(0,l.e)(i,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,l.q)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,l.q)(i,"direction")),wrongRTL:"-webkit-box"===(0,l.q)(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?m(t,e):e.addEventListener("load",e=>{m(t,e.target)})}),g(t),t.initialized=!0,g(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:n,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),n&&n.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,l.y)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,l.x)(O,e)}static get extendedDefaults(){return O}static get defaults(){return k}static installModule(e){D.prototype.__modules__||(D.prototype.__modules__=[]);let t=D.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>D.installModule(e)):D.installModule(e),D}}Object.keys(L).forEach(e=>{Object.keys(L[e]).forEach(t=>{D.prototype[t]=L[e][t]})}),D.use([function(e){let{swiper:t,on:i,emit:s}=e,r=(0,o.a)(),n=null,a=null,l=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver)return void(t&&!t.destroyed&&t.initialized&&(n=new ResizeObserver(e=>{a=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,n=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:a}=e;a&&a!==t.el||(r=s?s.width:(i[0]||i).inlineSize,n=s?s.height:(i[0]||i).blockSize)}),(r!==i||n!==s)&&l()})})).observe(t.el));r.addEventListener("resize",l),r.addEventListener("orientationchange",d)}),i("destroy",()=>{a&&r.cancelAnimationFrame(a),n&&n.unobserve&&t.el&&(n.unobserve(t.el),n=null),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",d)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,n=[],a=(0,o.a)(),d=function(e,i){void 0===i&&(i={});let s=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);let i=function(){r("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),n.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,l.b)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{n.forEach(e=>{e.disconnect()}),n.splice(0,n.length)})}]);let V=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function I(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function R(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:I(t[i])&&I(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:R(e[i],t[i]):e[i]=t[i]})}function j(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function B(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function F(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function _(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function z(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function N(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let G=(0,a.createContext)(null),H=(0,a.createContext)(null),W=(0,a.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:n="div",children:o,onSwiper:d,...u}=void 0===e?{}:e,h=!1,[c,p]=(0,a.useState)("swiper"),[m,f]=(0,a.useState)(null),[g,v]=(0,a.useState)(!1),y=(0,a.useRef)(!1),w=(0,a.useRef)(null),b=(0,a.useRef)(null),x=(0,a.useRef)(null),S=(0,a.useRef)(null),T=(0,a.useRef)(null),E=(0,a.useRef)(null),P=(0,a.useRef)(null),M=(0,a.useRef)(null),{params:C,passedParams:A,rest:L,events:O}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};R(i,k),i._emitClasses=!0,i.init=!1;let n={},a=V.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(o=>{void 0!==e[o]&&(a.indexOf(o)>=0?I(e[o])?(i[o]={},r[o]={},R(i[o],e[o]),R(r[o],e[o])):(i[o]=e[o],r[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"==typeof e[o]?t?s[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:i.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:n[o]=e[o])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:n,events:s}}(u),{slides:G,slots:W}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(z(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function e(t){let i=[];return a.Children.toArray(t).forEach(t=>{z(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(o),U=()=>{v(!g)};Object.assign(C.on,{_containerClasses(e,t){p(t)}});let q=()=>{Object.assign(C.on,O),h=!0;let e={...C};if(delete e.wrapperClass,b.current=new D(e),b.current.virtual&&b.current.params.virtual.enabled){b.current.virtual.slides=G;let e={cache:!1,slides:G,renderExternal:f,renderExternalUpdate:!1};R(b.current.params.virtual,e),R(b.current.originalParams.virtual,e)}};return w.current||q(),b.current&&b.current.on("_beforeBreakpoint",U),(0,a.useEffect)(()=>()=>{b.current&&b.current.off("_beforeBreakpoint",U)}),(0,a.useEffect)(()=>{!y.current&&b.current&&(b.current.emitSlidesClasses(),y.current=!0)}),N(()=>{if(t&&(t.current=w.current),w.current)return b.current.destroyed&&q(),!function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:n,scrollbarEl:a,swiper:o}=e;j(t)&&s&&r&&(o.params.navigation.nextEl=s,o.originalParams.navigation.nextEl=s,o.params.navigation.prevEl=r,o.originalParams.navigation.prevEl=r),B(t)&&n&&(o.params.pagination.el=n,o.originalParams.pagination.el=n),F(t)&&a&&(o.params.scrollbar.el=a,o.originalParams.scrollbar.el=a),o.init(i)}({el:w.current,nextEl:T.current,prevEl:E.current,paginationEl:P.current,scrollbarEl:M.current,swiper:b.current},C),d&&!b.current.destroyed&&d(b.current),()=>{b.current&&!b.current.destroyed&&b.current.destroy(!0,!1)}},[]),N(()=>{!h&&O&&b.current&&Object.keys(O).forEach(e=>{b.current.on(e,O[e])});let e=function(e,t,i,s,r){let n=[];if(!t)return n;let a=e=>{0>n.indexOf(e)&&n.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&a("children"),s.length!==i.length&&a("children")}return V.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t)if(I(e[i])&&I(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?a(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}))}else e[i]!==t[i]&&a(i)}),n}(A,x.current,G,S.current,e=>e.key);return x.current=A,S.current=G,e.length&&b.current&&!b.current.destroyed&&function(e){let t,i,s,r,n,a,o,d,{swiper:u,slides:h,passedParams:c,changedParams:p,nextEl:m,prevEl:f,scrollbarEl:g,paginationEl:v}=e,y=p.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:w,pagination:b,navigation:x,scrollbar:S,virtual:T,thumbs:E}=u;p.includes("thumbs")&&c.thumbs&&c.thumbs.swiper&&!c.thumbs.swiper.destroyed&&w.thumbs&&(!w.thumbs.swiper||w.thumbs.swiper.destroyed)&&(t=!0),p.includes("controller")&&c.controller&&c.controller.control&&w.controller&&!w.controller.control&&(i=!0),p.includes("pagination")&&c.pagination&&(c.pagination.el||v)&&(w.pagination||!1===w.pagination)&&b&&!b.el&&(s=!0),p.includes("scrollbar")&&c.scrollbar&&(c.scrollbar.el||g)&&(w.scrollbar||!1===w.scrollbar)&&S&&!S.el&&(r=!0),p.includes("navigation")&&c.navigation&&(c.navigation.prevEl||f)&&(c.navigation.nextEl||m)&&(w.navigation||!1===w.navigation)&&x&&!x.prevEl&&!x.nextEl&&(n=!0);let P=e=>{u[e]&&(u[e].destroy(),"navigation"===e?(u.isElement&&(u[e].prevEl.remove(),u[e].nextEl.remove()),w[e].prevEl=void 0,w[e].nextEl=void 0,u[e].prevEl=void 0,u[e].nextEl=void 0):(u.isElement&&u[e].el.remove(),w[e].el=void 0,u[e].el=void 0))};p.includes("loop")&&u.isElement&&(w.loop&&!c.loop?a=!0:!w.loop&&c.loop?o=!0:d=!0),y.forEach(e=>{if(I(w[e])&&I(c[e]))Object.assign(w[e],c[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in c[e]&&!c[e].enabled&&P(e);else{let t=c[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&P(e):w[e]=c[e]}}),y.includes("controller")&&!i&&u.controller&&u.controller.control&&w.controller&&w.controller.control&&(u.controller.control=w.controller.control),p.includes("children")&&h&&T&&w.virtual.enabled?(T.slides=h,T.update(!0)):p.includes("virtual")&&T&&w.virtual.enabled&&(h&&(T.slides=h),T.update(!0)),p.includes("children")&&h&&w.loop&&(d=!0),t&&E.init()&&E.update(!0),i&&(u.controller.control=w.controller.control),s&&(u.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-pagination"),v.part.add("pagination"),u.el.appendChild(v)),v&&(w.pagination.el=v),b.init(),b.render(),b.update()),r&&(u.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-scrollbar"),g.part.add("scrollbar"),u.el.appendChild(g)),g&&(w.scrollbar.el=g),S.init(),S.updateSize(),S.setTranslate()),n&&(u.isElement&&(m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-next"),(0,l.s)(m,u.hostEl.constructor.nextButtonSvg),m.part.add("button-next"),u.el.appendChild(m)),f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-prev"),(0,l.s)(f,u.hostEl.constructor.prevButtonSvg),f.part.add("button-prev"),u.el.appendChild(f))),m&&(w.navigation.nextEl=m),f&&(w.navigation.prevEl=f),x.init(),x.update()),p.includes("allowSlideNext")&&(u.allowSlideNext=c.allowSlideNext),p.includes("allowSlidePrev")&&(u.allowSlidePrev=c.allowSlidePrev),p.includes("direction")&&u.changeDirection(c.direction,!1),(a||d)&&u.loopDestroy(),(o||d)&&u.loopCreate(),u.update()}({swiper:b.current,slides:G,passedParams:A,changedParams:e,nextEl:T.current,prevEl:E.current,scrollbarEl:M.current,paginationEl:P.current}),()=>{O&&b.current&&Object.keys(O).forEach(e=>{b.current.off(e,O[e])})}}),N(()=>{var e;(e=b.current)&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())},[m]),a.createElement(r,$({ref:w,className:_(`${c}${s?` ${s}`:""}`)},L),a.createElement(H.Provider,{value:b.current},W["container-start"],a.createElement(n,{className:(void 0===(i=C.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},W["wrapper-start"],C.virtual?function(e,t,i){if(!i)return null;let s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:n,to:o}=i,l=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,u=[];for(let e=l;e<d;e+=1)e>=n&&e<=o&&u.push(t[s(e)]);return u.map((t,i)=>a.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(b.current,G,m):G.map((e,t)=>a.cloneElement(e,{swiper:b.current,swiperSlideIndex:t})),W["wrapper-end"]),j(C)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:E,className:"swiper-button-prev"}),a.createElement("div",{ref:T,className:"swiper-button-next"})),F(C)&&a.createElement("div",{ref:M,className:"swiper-scrollbar"}),B(C)&&a.createElement("div",{ref:P,className:"swiper-pagination"}),W["container-end"]))});W.displayName="Swiper";let U=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:n,zoom:o,lazy:l,virtualIndex:d,swiperSlideIndex:u,...h}=void 0===e?{}:e,c=(0,a.useRef)(null),[p,m]=(0,a.useState)("swiper-slide"),[f,g]=(0,a.useState)(!1);function v(e,t,i){t===c.current&&m(i)}N(()=>{if(void 0!==u&&(c.current.swiperSlideIndex=u),t&&(t.current=c.current),c.current&&n){if(n.destroyed){"swiper-slide"!==p&&m("swiper-slide");return}return n.on("_slideClass",v),()=>{n&&n.off("_slideClass",v)}}}),N(()=>{n&&c.current&&!n.destroyed&&m(n.getSlideClasses(c.current))},[n]);let y={isActive:p.indexOf("swiper-slide-active")>=0,isVisible:p.indexOf("swiper-slide-visible")>=0,isPrev:p.indexOf("swiper-slide-prev")>=0,isNext:p.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof s?s(y):s;return a.createElement(i,$({ref:c,className:_(`${p}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{g(!0)}},h),o&&a.createElement(G.Provider,{value:y},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof o?o:void 0},w(),l&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!o&&a.createElement(G.Provider,{value:y},w(),l&&!f&&a.createElement("div",{className:"swiper-lazy-preloader"})))});U.displayName="SwiperSlide"},7990:(e,t,i)=>{"use strict";i.d(t,{s:()=>r});var s=i(7594);function r(e){return(0,s.G)(e)&&"offsetHeight"in e}},8230:(e,t,i)=>{e.exports=i(1639)},8940:(e,t,i)=>{"use strict";function s(e,t,i,s){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return s}}),i(7810),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8953:()=>{},9099:(e,t,i)=>{e.exports=i(8253)},9315:(e,t,i)=>{"use strict";let s;function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function n(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function a(e,t,i,s){if("function"==typeof t){let[r,a]=n(s);t=t(void 0!==i?i:e.custom,r,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,a]=n(s);t=t(void 0!==i?i:e.custom,r,a)}return t}function o(e,t,i){let s=e.getProps();return a(s,t,void 0!==i?i:s.custom,e)}function l(e,t){return e?.[t]??e?.default??e}i.d(t,{P:()=>nf});let d=e=>e,u={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(e,t){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,a=h.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,s=new Set,r=!1,n=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function d(t){a.has(t)&&(u.schedule(t),e()),l++,t(o)}let u={schedule:(e,t=!1,n=!1)=>{let o=n&&r?i:s;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{s.delete(e),a.delete(e)},process:e=>{if(o=e,r){n=!0;return}r=!0,[i,s]=[s,i],i.forEach(d),t&&c.value&&c.value.frameloop[t].push(l),l=0,i.clear(),r=!1,n&&(n=!1,u.process(e))}};return u}(n,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:d,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=a,y=()=>{let n=u.useManualTiming?r.timestamp:performance.now();i=!1,u.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1)),r.timestamp=n,r.isProcessing=!0,o.process(r),l.process(r),d.process(r),p.process(r),m.process(r),f.process(r),g.process(r),v.process(r),r.isProcessing=!1,i&&t&&(s=!1,e(y))};return{schedule:h.reduce((t,n)=>{let o=a[n];return t[n]=(t,n=!1,a=!1)=>(!i&&(i=!0,s=!0,r.isProcessing||e(y)),o.schedule(t,n,a)),t},{}),cancel:e=>{for(let t=0;t<h.length;t++)a[h[t]].cancel(e)},state:r,steps:a}}let{schedule:m,cancel:f,state:g,steps:v}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:d,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],w=new Set(y),b=new Set(["width","height","top","left","right","bottom",...y]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function S(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>S(this.subscriptions,e)}notify(e,t,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](e,t,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function E(){s=void 0}let P={now:()=>(void 0===s&&P.set(g.isProcessing||u.useManualTiming?g.timestamp:performance.now()),s),set:e=>{s=e,queueMicrotask(E)}},M={current:void 0};class C{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=P.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=P.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new T);let i=this.events[e].add(t);return"change"===e?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=P.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function A(e,t){return new C(e,t)}let k=e=>Array.isArray(e),L=e=>!!(e&&e.getVelocity);function O(e,t){let i=e.getValue("willChange");if(L(i)&&i.add)return i.add(t);if(!i&&u.WillChange){let i=new u.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let D=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),V="data-"+D("framerAppearId"),I=(e,t)=>i=>t(e(i)),R=(...e)=>e.reduce(I),j=(e,t,i)=>i>t?t:i<e?e:i,B=e=>1e3*e,F={layout:0,mainThread:0,waapi:0},_=()=>{},$=()=>{},z=e=>t=>"string"==typeof t&&t.startsWith(e),N=z("--"),G=z("var(--"),H=e=>!!G(e)&&W.test(e.split("/*")[0].trim()),W=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,U={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},q={...U,transform:e=>j(0,1,e)},Y={...U,default:1},X=e=>Math.round(1e5*e)/1e5,K=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Z=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Q=(e,t)=>i=>!!("string"==typeof i&&Z.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),J=(e,t,i)=>s=>{if("string"!=typeof s)return s;let[r,n,a,o]=s.match(K);return{[e]:parseFloat(r),[t]:parseFloat(n),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ee={...U,transform:e=>Math.round(j(0,255,e))},et={test:Q("rgb","red"),parse:J("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:s=1})=>"rgba("+ee.transform(e)+", "+ee.transform(t)+", "+ee.transform(i)+", "+X(q.transform(s))+")"},ei={test:Q("#"),parse:function(e){let t="",i="",s="",r="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,i+=i,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:et.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),er=es("deg"),en=es("%"),ea=es("px"),eo=es("vh"),el=es("vw"),ed={...en,parse:e=>en.parse(e)/100,transform:e=>en.transform(100*e)},eu={test:Q("hsl","hue"),parse:J("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:s=1})=>"hsla("+Math.round(e)+", "+en.transform(X(t))+", "+en.transform(X(i))+", "+X(q.transform(s))+")"},eh={test:e=>et.test(e)||ei.test(e)||eu.test(e),parse:e=>et.test(e)?et.parse(e):eu.test(e)?eu.parse(e):ei.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?et.transform(e):eu.transform(e),getAnimatableNone:e=>{let t=eh.parse(e);return t.alpha=0,eh.transform(t)}},ec=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ep="number",em="color",ef=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eg(e){let t=e.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,a=t.replace(ef,e=>(eh.test(e)?(s.color.push(n),r.push(em),i.push(eh.parse(e))):e.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(e)):(s.number.push(n),r.push(ep),i.push(parseFloat(e))),++n,"${}")).split("${}");return{values:i,split:a,indexes:s,types:r}}function ev(e){return eg(e).values}function ey(e){let{split:t,types:i}=eg(e),s=t.length;return e=>{let r="";for(let n=0;n<s;n++)if(r+=t[n],void 0!==e[n]){let t=i[n];t===ep?r+=X(e[n]):t===em?r+=eh.transform(e[n]):r+=e[n]}return r}}let ew=e=>"number"==typeof e?0:eh.test(e)?eh.getAnimatableNone(e):e,eb={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(K)?.length||0)+(e.match(ec)?.length||0)>0},parse:ev,createTransformer:ey,getAnimatableNone:function(e){let t=ev(e);return ey(e)(t.map(ew))}};function ex(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function eS(e,t){return i=>i>0?t:e}let eT=(e,t,i)=>e+(t-e)*i,eE=(e,t,i)=>{let s=e*e,r=i*(t*t-s)+s;return r<0?0:Math.sqrt(r)},eP=[ei,et,eu];function eM(e){let t=eP.find(t=>t.test(e));if(_(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===eu&&(i=function({hue:e,saturation:t,lightness:i,alpha:s}){e/=360,i/=100;let r=0,n=0,a=0;if(t/=100){let s=i<.5?i*(1+t):i+t-i*t,o=2*i-s;r=ex(o,s,e+1/3),n=ex(o,s,e),a=ex(o,s,e-1/3)}else r=n=a=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:s}}(i)),i}let eC=(e,t)=>{let i=eM(e),s=eM(t);if(!i||!s)return eS(e,t);let r={...i};return e=>(r.red=eE(i.red,s.red,e),r.green=eE(i.green,s.green,e),r.blue=eE(i.blue,s.blue,e),r.alpha=eT(i.alpha,s.alpha,e),et.transform(r))},eA=new Set(["none","hidden"]);function ek(e,t){return i=>eT(e,t,i)}function eL(e){return"number"==typeof e?ek:"string"==typeof e?H(e)?eS:eh.test(e)?eC:eV:Array.isArray(e)?eO:"object"==typeof e?eh.test(e)?eC:eD:eS}function eO(e,t){let i=[...e],s=i.length,r=e.map((e,i)=>eL(e)(e,t[i]));return e=>{for(let t=0;t<s;t++)i[t]=r[t](e);return i}}function eD(e,t){let i={...e,...t},s={};for(let r in i)void 0!==e[r]&&void 0!==t[r]&&(s[r]=eL(e[r])(e[r],t[r]));return e=>{for(let t in s)i[t]=s[t](e);return i}}let eV=(e,t)=>{let i=eb.createTransformer(t),s=eg(e),r=eg(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?eA.has(e)&&!r.values.length||eA.has(t)&&!s.values.length?function(e,t){return eA.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):R(eO(function(e,t){let i=[],s={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){let n=t.types[r],a=e.indexes[n][s[n]],o=e.values[a]??0;i[r]=o,s[n]++}return i}(s,r),r.values),i):(_(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eS(e,t))};function eI(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?eT(e,t,i):eL(e)(e,t)}let eR=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>m.update(t,e),stop:()=>f(t),now:()=>g.isProcessing?g.timestamp:P.now()}},ej=(e,t,i=10)=>{let s="",r=Math.max(Math.round(t/i),2);for(let t=0;t<r;t++)s+=Math.round(1e4*e(t/(r-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function eB(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function eF(e,t,i){var s,r;let n=Math.max(t-5,0);return s=i-e(n),(r=t-n)?1e3/r*s:0}let e_={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let ez=["duration","bounce"],eN=["stiffness","damping","mass"];function eG(e,t){return t.some(t=>void 0!==e[t])}function eH(e=e_.visualDuration,t=e_.bounce){let i,s="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:n}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:d,damping:u,mass:h,duration:c,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:e_.velocity,stiffness:e_.stiffness,damping:e_.damping,mass:e_.mass,isResolvedFromDuration:!1,...e};if(!eG(e,eN)&&eG(e,ez))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),s=i*i,r=2*j(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:e_.mass,stiffness:s,damping:r}}else{let i=function({duration:e=e_.duration,bounce:t=e_.bounce,velocity:i=e_.velocity,mass:s=e_.mass}){let r,n;_(e<=B(e_.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=j(e_.minDamping,e_.maxDamping,a),e=j(e_.minDuration,e_.maxDuration,e/1e3),a<1?(r=t=>{let s=t*a,r=s*e;return .001-(s-i)/e$(t,a)*Math.exp(-r)},n=t=>{let s=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=Math.exp(-s),l=e$(Math.pow(t,2),a);return(s*i+i-n)*o*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),n=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let s=i;for(let i=1;i<12;i++)s-=e(s)/t(s);return s}(r,n,5/e);if(e=B(e),isNaN(o))return{stiffness:e_.stiffness,damping:e_.damping,duration:e};{let t=Math.pow(o,2)*s;return{stiffness:t,damping:2*a*Math.sqrt(s*t),duration:e}}}(e);(t={...t,...i,mass:e_.mass}).isResolvedFromDuration=!0}return t}({...s,velocity:-((s.velocity||0)/1e3)}),f=p||0,g=u/(2*Math.sqrt(d*h)),v=o-a,y=Math.sqrt(d/h)/1e3,w=5>Math.abs(v);if(r||(r=w?e_.restSpeed.granular:e_.restSpeed.default),n||(n=w?e_.restDelta.granular:e_.restDelta.default),g<1){let e=e$(y,g);i=t=>o-Math.exp(-g*y*t)*((f+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)i=e=>o-Math.exp(-y*e)*(v+(f+y*v)*e);else{let e=y*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*y*t),s=Math.min(e*t,300);return o-i*((f+g*y*v)*Math.sinh(s)+e*v*Math.cosh(s))/e}}let b={calculatedDuration:m&&c||null,next:e=>{let t=i(e);if(m)l.done=e>=c;else{let s=0===e?f:0;g<1&&(s=0===e?B(f):eF(i,e,t));let a=Math.abs(o-t)<=n;l.done=Math.abs(s)<=r&&a}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eB(b),2e4),t=ej(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eW({keyframes:e,velocity:t=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:u}){let h,c,p=e[0],m={done:!1,value:p},f=i*t,g=p+f,v=void 0===a?g:a(g);v!==g&&(f=v-p);let y=e=>-f*Math.exp(-e/s),w=e=>v+y(e),b=e=>{let t=y(e),i=w(e);m.done=Math.abs(t)<=d,m.value=m.done?v:i},x=e=>{let t;if(t=m.value,void 0!==o&&t<o||void 0!==l&&t>l){var i;h=e,c=eH({keyframes:[m.value,(i=m.value,void 0===o?l:void 0===l||Math.abs(o-i)<Math.abs(l-i)?o:l)],velocity:eF(w,e,m.value),damping:r,stiffness:n,restDelta:d,restSpeed:u})}};return x(0),{calculatedDuration:null,next:e=>{let t=!1;return(c||void 0!==h||(t=!0,b(e),x(e)),void 0!==h&&e>=h)?c.next(e-h):(t||b(e),m)}}}eH.applyToOptions=e=>{let t=function(e,t=100,i){let s=i({...e,keyframes:[0,t]}),r=Math.min(eB(s),2e4);return{type:"keyframes",ease:e=>s.next(r*e).value/t,duration:r/1e3}}(e,100,eH);return e.ease=t.ease,e.duration=B(t.duration),e.type="keyframes",e};let eU=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function eq(e,t,i,s){return e===t&&i===s?d:r=>0===r||1===r?r:eU(function(e,t,i,s,r){let n,a,o=0;do(n=eU(a=t+(i-t)/2,s,r)-e)>0?i=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a}(r,0,1,e,i),t,s)}let eY=eq(.42,0,1,1),eX=eq(0,0,.58,1),eK=eq(.42,0,.58,1),eZ=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eQ=e=>t=>1-e(1-t),eJ=eq(.33,1.53,.69,.99),e0=eQ(eJ),e1=eZ(e0),e2=e=>(e*=2)<1?.5*e0(e):.5*(2-Math.pow(2,-10*(e-1))),e3=e=>1-Math.sin(Math.acos(e)),e5=eQ(e3),e4=eZ(e3),e8=e=>Array.isArray(e)&&"number"==typeof e[0],e9={linear:d,easeIn:eY,easeInOut:eK,easeOut:eX,circIn:e3,circInOut:e4,circOut:e5,backIn:e0,backInOut:e1,backOut:eJ,anticipate:e2},e6=e=>{if(e8(e)){$(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,s,r]=e;return eq(t,i,s,r)}return"string"==typeof e?($(void 0!==e9[e],`Invalid easing type '${e}'`),e9[e]):e},e7=(e,t,i)=>{let s=t-e;return 0===s?1:(i-e)/s};function te({duration:e=300,keyframes:t,times:i,ease:s="easeInOut"}){var r;let n=Array.isArray(s)&&"number"!=typeof s[0]?s.map(e6):e6(s),a={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:s,mixer:r}={}){let n=e.length;if($(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];if(2===n&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let s=[],r=i||u.mix||eI,n=e.length-1;for(let i=0;i<n;i++){let n=r(e[i],e[i+1]);t&&(n=R(Array.isArray(t)?t[i]||d:t,n)),s.push(n)}return s}(t,s,r),l=o.length,h=i=>{if(a&&i<e[0])return t[0];let s=0;if(l>1)for(;s<e.length-2&&!(i<e[s+1]);s++);let r=e7(e[s],e[s+1],i);return o[s](r)};return i?t=>h(j(e[0],e[n-1],t)):h}((r=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let s=1;s<=t;s++){let r=e7(0,t,s);e.push(eT(i,1,r))}}(t,e.length-1),t}(t),r.map(t=>t*e)),t,{ease:Array.isArray(n)?n:t.map(()=>n||eK).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=o(t),a.done=t>=e,a)}}let tt=e=>null!==e;function ti(e,{repeat:t,repeatType:i="loop"},s,r=1){let n=e.filter(tt),a=r<0||t&&"loop"!==i&&t%2==1?0:n.length-1;return a&&void 0!==s?s:n[a]}let ts={decay:eW,inertia:eW,tween:te,keyframes:te,spring:eH};function tr(e){"string"==typeof e.type&&(e.type=ts[e.type])}class tn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let ta=e=>e/100;class to extends tn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==P.now()&&this.tick(P.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tr(e);let{type:t=te,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:n=0}=e,{keyframes:a}=e,o=t||te;o!==te&&"number"!=typeof a[0]&&(this.mixKeyframes=R(ta,eI(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===r&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:d}=l;this.calculatedDuration=d,this.resolvedDuration=d+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:s,mixKeyframes:r,mirroredGenerator:n,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:d,repeat:u,repeatType:h,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-s/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let y=this.currentTime,w=i;if(u){let e=Math.min(this.currentTime,s)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,u+1))%2&&("reverse"===h?(i=1-i,c&&(i-=c/a)):"mirror"===h&&(w=n)),y=j(0,1,i)*a}let b=v?{done:!1,value:d[0]}:w.next(y);r&&(b.value=r(b.value));let{done:x}=b;v||null===o||(x=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return S&&p!==eW&&(b.value=ti(d,this.options,f,this.speed)),m&&m(b.value),S&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){e=B(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(P.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:e=eR,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(P.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tl=e=>180*e/Math.PI,td=e=>th(tl(Math.atan2(e[1],e[0]))),tu={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:td,rotateZ:td,skewX:e=>tl(Math.atan(e[1])),skewY:e=>tl(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},th=e=>((e%=360)<0&&(e+=360),e),tc=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tp=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tm={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tc,scaleY:tp,scale:e=>(tc(e)+tp(e))/2,rotateX:e=>th(tl(Math.atan2(e[6],e[5]))),rotateY:e=>th(tl(Math.atan2(-e[2],e[0]))),rotateZ:td,rotate:td,skewX:e=>tl(Math.atan(e[4])),skewY:e=>tl(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tf(e){return+!!e.includes("scale")}function tg(e,t){let i,s;if(!e||"none"===e)return tf(t);let r=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=tm,s=r;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tu,s=t}if(!s)return tf(t);let n=i[t],a=s[1].split(",").map(tv);return"function"==typeof n?n(a):a[n]}function tv(e){return parseFloat(e.trim())}let ty=e=>e===U||e===ea,tw=new Set(["x","y","z"]),tb=y.filter(e=>!tw.has(e)),tx={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tg(t,"x"),y:(e,{transform:t})=>tg(t,"y")};tx.translateX=tx.x,tx.translateY=tx.y;let tS=new Set,tT=!1,tE=!1,tP=!1;function tM(){if(tE){let e=Array.from(tS).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return tb.forEach(i=>{let s=e.getValue(i);void 0!==s&&(t.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tE=!1,tT=!1,tS.forEach(e=>e.complete(tP)),tS.clear()}function tC(){tS.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tE=!0)})}class tA{constructor(e,t,i,s,r,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(tS.add(this),tT||(tT=!0,m.read(tC),m.resolveKeyframes(tM))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:s}=this;if(null===e[0]){let r=s?.get(),n=e[e.length-1];if(void 0!==r)e[0]=r;else if(i&&t){let s=i.readValue(t,n);null!=s&&(e[0]=s)}void 0===e[0]&&(e[0]=n),s&&void 0===r&&s.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tS.delete(this)}cancel(){"scheduled"===this.state&&(tS.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}function tk(e){let t;return()=>(void 0===t&&(t=e()),t)}let tL=tk(()=>void 0!==window.ScrollTimeline),tO={},tD=function(e,t){let i=tk(e);return()=>tO[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tV=([e,t,i,s])=>`cubic-bezier(${e}, ${t}, ${i}, ${s})`,tI={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tV([0,.65,.55,1]),circOut:tV([.55,0,1,.45]),backIn:tV([.31,.01,.66,-.59]),backOut:tV([.33,1.53,.69,.99])};function tR(e){return"function"==typeof e&&"applyToOptions"in e}class tj extends tn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:s,pseudoElement:r,allowFlatten:n=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!r,this.allowFlatten=n,this.options=e,$("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tR(e)&&tD()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:a="loop",ease:o="easeOut",times:l}={},d){let u={[t]:i};l&&(u.offset=l);let h=function e(t,i){if(t)return"function"==typeof t?tD()?ej(t,i):"ease-out":e8(t)?tV(t):Array.isArray(t)?t.map(t=>e(t,i)||tI.easeOut):tI[t]}(o,r);Array.isArray(h)&&(u.easing=h),c.value&&F.waapi++;let p={delay:s,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"};d&&(p.pseudoElement=d);let m=e.animate(u,p);return c.value&&m.finished.finally(()=>{F.waapi--}),m}(t,i,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let e=ti(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){t.startsWith("--")?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=B(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tL())?(this.animation.timeline=e,d):t(this)}}let tB={anticipate:e2,backInOut:e1,circInOut:e4};class tF extends tj{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tB&&(e.ease=tB[e.ease])}(e),tr(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:s,element:r,...n}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new to({...n,autoplay:!1}),o=B(this.finishedTime??this.time);t.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let t_=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eb.test(e)||"0"===e)&&!e.startsWith("url("));var t$,tz,tN=i(7990);let tG=new Set(["opacity","clipPath","filter","transform"]),tH=tk(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tW extends tn{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",keyframes:a,name:o,motionValue:l,element:d,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=P.now();let h={autoplay:e,delay:t,type:i,repeat:s,repeatDelay:r,repeatType:n,name:o,motionValue:l,element:d,...u},c=d?.KeyframeResolver||tA;this.keyframeResolver=new c(a,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),o,l,d),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,s){this.keyframeResolver=void 0;let{name:r,type:n,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=P.now(),!function(e,t,i,s){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let n=e[e.length-1],a=t_(r,t),o=t_(n,t);return _(a===o,`You are trying to animate ${t} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||tR(i))&&s)}(e,r,n,a)&&((u.instantAnimations||!o)&&h?.(ti(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let c={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},p=!l&&function(e){let{motionValue:t,name:i,repeatDelay:s,repeatType:r,damping:n,type:a}=e;if(!(0,tN.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tH()&&i&&tG.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==r&&0!==n&&"inertia"!==a}(c)?new tF({...c,element:c.motionValue.owner.current}):new to(c);p.finished.then(()=>this.notifyFinished()).catch(d),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tP=!0,tC(),tM(),tP=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tU=e=>null!==e,tq={type:"spring",stiffness:500,damping:25,restSpeed:10},tY={type:"keyframes",duration:.8},tX={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tK=(e,t,i,s={},r,n)=>a=>{let o=l(s,e)||{},d=o.delay||s.delay||0,{elapsed:h=0}=s;h-=B(d);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-h,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:n?void 0:r};!function({when:e,delay:t,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:d,...u}){return!!Object.keys(u).length}(o)&&Object.assign(c,((e,{keyframes:t})=>t.length>2?tY:w.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:tq:tX)(e,c)),c.duration&&(c.duration=B(c.duration)),c.repeatDelay&&(c.repeatDelay=B(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(u.instantAnimations||u.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,p&&!n&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},s){let r=e.filter(tU),n=t&&"loop"!==i&&t%2==1?0:r.length-1;return r[n]}(c.keyframes,o);if(void 0!==e)return void m.update(()=>{c.onUpdate(e),c.onComplete()})}return o.isSync?new to(c):new tW(c)};function tZ(e,t,{delay:i=0,transitionOverride:s,type:r}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:a,...d}=t;s&&(n=s);let u=[],h=r&&e.animationState&&e.animationState.getState()[r];for(let t in d){let s=e.getValue(t,e.latestValues[t]??null),r=d[t];if(void 0===r||h&&function({protectedKeys:e,needsAnimating:t},i){let s=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,s}(h,t))continue;let a={delay:i,...l(n||{},t)},o=s.get();if(void 0!==o&&!s.isAnimating&&!Array.isArray(r)&&r===o&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=e.props[V];if(i){let e=window.MotionHandoffAnimation(i,t,m);null!==e&&(a.startTime=e,c=!0)}}O(e,t),s.start(tK(t,s,r,e.shouldReduceMotion&&b.has(t)?{type:!1}:a,e,c));let p=s.animation;p&&u.push(p)}return a&&Promise.all(u).then(()=>{m.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:s={},...r}=o(e,t)||{};for(let t in r={...r,...i}){var n;let i=k(n=r[t])?n[n.length-1]||0:n;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,A(i))}}(e,a)})}),u}function tQ(e,t,i={}){let s=o(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:r=e.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let n=s?()=>Promise.all(tZ(e,s,i)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,i=0,s=0,r=1,n){let a=[],o=(e.variantChildren.size-1)*s,l=1===r?(e=0)=>e*s:(e=0)=>o-e*s;return Array.from(e.variantChildren).sort(tJ).forEach((e,s)=>{e.notify("AnimationStart",t),a.push(tQ(e,t,{...n,delay:i+l(s)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+s,a,o,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([n(),a(i.delay)]);{let[e,t]="beforeChildren"===l?[n,a]:[a,n];return e().then(()=>t())}}function tJ(e,t){return e.sortNodePosition(t)}function t0(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let s=0;s<i;s++)if(t[s]!==e[s])return!1;return!0}function t1(e){return"string"==typeof e||Array.isArray(e)}let t2=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],t3=["initial",...t2],t5=t3.length,t4=[...t2].reverse(),t8=t2.length;function t9(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function t6(){return{animate:t9(!0),whileInView:t9(),whileHover:t9(),whileTap:t9(),whileDrag:t9(),whileFocus:t9(),exit:t9()}}class t7{constructor(e){this.isMounted=!1,this.node=e}update(){}}class ie extends t7{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let s;if(e.notify("AnimationStart",t),Array.isArray(t))s=Promise.all(t.map(t=>tQ(e,t,i)));else if("string"==typeof t)s=tQ(e,t,i);else{let r="function"==typeof t?o(e,t,i.custom):t;s=Promise.all(tZ(e,r,i))}return s.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=t6(),s=!0,n=t=>(i,s)=>{let r=o(e,s,"exit"===t?e.presenceContext?.custom:void 0);if(r){let{transition:e,transitionEnd:t,...s}=r;i={...i,...s,...t}}return i};function a(a){let{props:l}=e,d=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<t5;e++){let s=t3[e],r=t.props[s];(t1(r)||!1===r)&&(i[s]=r)}return i}(e.parent)||{},u=[],h=new Set,c={},p=1/0;for(let t=0;t<t8;t++){var m,f;let o=t4[t],g=i[o],v=void 0!==l[o]?l[o]:d[o],y=t1(v),w=o===a?g.isActive:null;!1===w&&(p=t);let b=v===d[o]&&v!==l[o]&&y;if(b&&s&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===w||!v&&!g.prevProp||r(v)||"boolean"==typeof v)continue;let x=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!t0(f,m)),S=x||o===a&&g.isActive&&!b&&y||t>p&&y,T=!1,E=Array.isArray(v)?v:[v],P=E.reduce(n(o),{});!1===w&&(P={});let{prevResolvedValues:M={}}=g,C={...M,...P},A=t=>{S=!0,h.has(t)&&(T=!0,h.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in C){let t=P[e],i=M[e];if(!c.hasOwnProperty(e))(k(t)&&k(i)?t0(t,i):t===i)?void 0!==t&&h.has(e)?A(e):g.protectedKeys[e]=!0:null!=t?A(e):h.add(e)}g.prevProp=v,g.prevResolvedValues=P,g.isActive&&(c={...c,...P}),s&&e.blockInitialAnimation&&(S=!1);let L=!(b&&x)||T;S&&L&&u.push(...E.map(e=>({animation:e,options:{type:o}})))}if(h.size){let t={};if("boolean"!=typeof l.initial){let i=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(t.transition=i.transition)}h.forEach(i=>{let s=e.getBaseTarget(i),r=e.getValue(i);r&&(r.liveStyle=!0),t[i]=s??null}),u.push({animation:t})}let g=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),s=!1,g?t(u):Promise.resolve()}return{animateChanges:a,setActive:function(t,s){if(i[t].isActive===s)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,s)),i[t].isActive=s;let r=a(t);for(let e in i)i[e].protectedKeys={};return r},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=t6(),s=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let it=0;class ii extends t7{constructor(){super(...arguments),this.id=it++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let s=this.node.animationState.setActive("exit",!e);t&&!e&&s.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let is={x:!1,y:!1};function ir(e,t,i,s={passive:!0}){return e.addEventListener(t,i,s),()=>e.removeEventListener(t,i)}let ia=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function io(e){return{point:{x:e.pageX,y:e.pageY}}}function il(e,t,i,s){return ir(e,t,e=>ia(e)&&i(e,io(e)),s)}function id({top:e,left:t,right:i,bottom:s}){return{x:{min:t,max:i},y:{min:e,max:s}}}function iu(e){return e.max-e.min}function ih(e,t,i,s=.5){e.origin=s,e.originPoint=eT(t.min,t.max,e.origin),e.scale=iu(i)/iu(t),e.translate=eT(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function ic(e,t,i,s){ih(e.x,t.x,i.x,s?s.originX:void 0),ih(e.y,t.y,i.y,s?s.originY:void 0)}function ip(e,t,i){e.min=i.min+t.min,e.max=e.min+iu(t)}function im(e,t,i){e.min=t.min-i.min,e.max=e.min+iu(t)}function ig(e,t,i){im(e.x,t.x,i.x),im(e.y,t.y,i.y)}let iv=()=>({translate:0,scale:1,origin:0,originPoint:0}),iy=()=>({x:iv(),y:iv()}),iw=()=>({min:0,max:0}),ib=()=>({x:iw(),y:iw()});function ix(e){return[e("x"),e("y")]}function iS(e){return void 0===e||1===e}function iT({scale:e,scaleX:t,scaleY:i}){return!iS(e)||!iS(t)||!iS(i)}function iE(e){return iT(e)||iP(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function iP(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iM(e,t,i,s,r){return void 0!==r&&(e=s+r*(e-s)),s+i*(e-s)+t}function iC(e,t=0,i=1,s,r){e.min=iM(e.min,t,i,s,r),e.max=iM(e.max,t,i,s,r)}function iA(e,{x:t,y:i}){iC(e.x,t.translate,t.scale,t.originPoint),iC(e.y,i.translate,i.scale,i.originPoint)}function ik(e,t){e.min=e.min+t,e.max=e.max+t}function iL(e,t,i,s,r=.5){let n=eT(e.min,e.max,r);iC(e,t,i,n,s)}function iO(e,t){iL(e.x,t.x,t.scaleX,t.scale,t.originX),iL(e.y,t.y,t.scaleY,t.scale,t.originY)}function iD(e,t){return id(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),s=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(e.getBoundingClientRect(),t))}let iV=({current:e})=>e?e.ownerDocument.defaultView:null;function iI(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let iR=(e,t)=>Math.abs(e-t);class ij{constructor(e,t,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=i_(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(iR(e.x,t.x)**2+iR(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:s}=e,{timestamp:r}=g;this.history.push({...s,timestamp:r});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=iB(t,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=i_("pointercancel"===e.type?this.lastMoveEventInfo:iB(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,n),s&&s(e,n)},!ia(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=i,this.contextWindow=s||window;let n=iB(io(e),this.transformPagePoint),{point:a}=n,{timestamp:o}=g;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,i_(n,this.history)),this.removeListeners=R(il(this.contextWindow,"pointermove",this.handlePointerMove),il(this.contextWindow,"pointerup",this.handlePointerUp),il(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iB(e,t){return t?{point:t(e.point)}:e}function iF(e,t){return{x:e.x-t.x,y:e.y-t.y}}function i_({point:e},t){return{point:e,delta:iF(e,i$(t)),offset:iF(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,s=null,r=i$(e);for(;i>=0&&(s=e[i],!(r.timestamp-s.timestamp>B(.1)));)i--;if(!s)return{x:0,y:0};let n=(r.timestamp-s.timestamp)/1e3;if(0===n)return{x:0,y:0};let a={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function i$(e){return e[e.length-1]}function iz(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function iN(e,t){let i=t.min-e.min,s=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,s]=[s,i]),{min:i,max:s}}function iG(e,t,i){return{min:iH(e,t),max:iH(e,i)}}function iH(e,t){return"number"==typeof e?e:e[t]||0}let iW=new WeakMap;class iU{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ib(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new ij(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(io(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(is[e])return null;else return is[e]=!0,()=>{is[e]=!1};return is.x||is.y?null:(is.x=is.y=!0,()=>{is.x=is.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ix(e=>{let t=this.getAxisMotionValue(e).get()||0;if(en.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[e];s&&(t=iu(s)*(parseFloat(t)/100))}}this.originPoint[e]=t}),r&&m.postRender(()=>r(e,t)),O(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(s&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ix(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iV(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=t;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:s}=this.getProps();if(!i||!iq(e,s,this.currentDirection))return;let r=this.getAxisMotionValue(e),n=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:i},s){return void 0!==t&&e<t?e=s?eT(t,e,s.min):Math.max(e,t):void 0!==i&&e>i&&(e=s?eT(i,e,s.max):Math.min(e,i)),e}(n,this.constraints[e],this.elastic[e])),r.set(n)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;e&&iI(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:s,right:r}){return{x:iz(e.x,i,r),y:iz(e.y,t,s)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:iG(e,"left","right"),y:iG(e,"top","bottom")}}(t),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ix(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!iI(t))return!1;let s=t.current;$(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(e,t,i){let s=iD(e,i),{scroll:r}=t;return r&&(ik(s.x,r.offset.x),ik(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),a=(e=r.layout.layoutBox,{x:iN(e.x,n.x),y:iN(e.y,n.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=id(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(ix(a=>{if(!iq(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let d={type:"inertia",velocity:i?e[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return O(this.visualElement,e),i.start(tK(e,i,0,t,this.visualElement,!1))}stopAnimation(){ix(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ix(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){ix(t=>{let{drag:i}=this.getProps();if(!iq(t,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(t);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[t];r.set(e[t]-eT(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!iI(t)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};ix(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();s[e]=function(e,t){let i=.5,s=iu(e),r=iu(t);return r>s?i=e7(t.min,t.max-s,e.min):s>r&&(i=e7(e.min,e.max-r,t.min)),j(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ix(t=>{if(!iq(t,e,null))return;let i=this.getAxisMotionValue(t),{min:r,max:n}=this.constraints[t];i.set(eT(r,n,s[t]))})}addListeners(){if(!this.visualElement.current)return;iW.set(this.visualElement,this);let e=il(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();iI(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(t);let r=ir(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(ix(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function iq(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class iY extends t7{constructor(e){super(e),this.removeGroupControls=d,this.removeListeners=d,this.controls=new iU(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||d}unmount(){this.removeGroupControls(),this.removeListeners()}}let iX=e=>(t,i)=>{e&&m.postRender(()=>e(t,i))};class iK extends t7{constructor(){super(...arguments),this.removePointerDownListener=d}onPointerDown(e){this.session=new ij(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iV(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:iX(e),onStart:iX(t),onMove:i,onEnd:(e,t)=>{delete this.session,s&&m.postRender(()=>s(e,t))}}}mount(){this.removePointerDownListener=il(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iZ=i(7876);let{schedule:iQ}=p(queueMicrotask,!1);var iJ=i(4232),i0=i(3885),i1=i(5048);let i2=(0,iJ.createContext)({}),i3={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function i5(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let i4={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!ea.test(e))return e;else e=parseFloat(e);let i=i5(e,t.target.x),s=i5(e,t.target.y);return`${i}% ${s}%`}},i8={};class i9 extends iJ.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=e;for(let e in i7)i8[e]=i7[e],N(e)&&(i8[e].isCSSVariable=!0);r&&(t.group&&t.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),i3.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:s,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,s||e.layoutDependency!==t||void 0===t||e.isPresent!==r?n.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?n.promote():n.relegate()||m.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),iQ.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:s}=e;s&&(s.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function i6(e){let[t,i]=(0,i0.xQ)(),s=(0,iJ.useContext)(i1.L);return(0,iZ.jsx)(i9,{...e,layoutGroup:s,switchLayoutGroup:(0,iJ.useContext)(i2),isPresent:t,safeToRemove:i})}let i7={borderRadius:{...i4,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i4,borderTopRightRadius:i4,borderBottomLeftRadius:i4,borderBottomRightRadius:i4,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let s=eb.parse(e);if(s.length>5)return e;let r=eb.createTransformer(e),n=+("number"!=typeof s[0]),a=i.x.scale*t.x,o=i.y.scale*t.y;s[0+n]/=a,s[1+n]/=o;let l=eT(a,o,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}};var se=i(7594);function st(e){return(0,se.G)(e)&&"ownerSVGElement"in e}let si=(e,t)=>e.depth-t.depth;class ss{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){S(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(si),this.isDirty=!1,this.children.forEach(e)}}function sr(e){return L(e)?e.get():e}let sn=["TopLeft","TopRight","BottomLeft","BottomRight"],sa=sn.length,so=e=>"string"==typeof e?parseFloat(e):e,sl=e=>"number"==typeof e||ea.test(e);function sd(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let su=sc(0,.5,e5),sh=sc(.5,.95,d);function sc(e,t,i){return s=>s<e?0:s>t?1:i(e7(e,t,s))}function sp(e,t){e.min=t.min,e.max=t.max}function sm(e,t){sp(e.x,t.x),sp(e.y,t.y)}function sf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function sg(e,t,i,s,r){return e-=t,e=s+1/i*(e-s),void 0!==r&&(e=s+1/r*(e-s)),e}function sv(e,t,[i,s,r],n,a){!function(e,t=0,i=1,s=.5,r,n=e,a=e){if(en.test(t)&&(t=parseFloat(t),t=eT(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=eT(n.min,n.max,s);e===n&&(o-=t),e.min=sg(e.min,t,i,o,r),e.max=sg(e.max,t,i,o,r)}(e,t[i],t[s],t[r],t.scale,n,a)}let sy=["x","scaleX","originX"],sw=["y","scaleY","originY"];function sb(e,t,i,s){sv(e.x,t,sy,i?i.x:void 0,s?s.x:void 0),sv(e.y,t,sw,i?i.y:void 0,s?s.y:void 0)}function sx(e){return 0===e.translate&&1===e.scale}function sS(e){return sx(e.x)&&sx(e.y)}function sT(e,t){return e.min===t.min&&e.max===t.max}function sE(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function sP(e,t){return sE(e.x,t.x)&&sE(e.y,t.y)}function sM(e){return iu(e.x)/iu(e.y)}function sC(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class sA{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(S(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:s}=e.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sk={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sL=["","X","Y","Z"],sO={visibility:"hidden"},sD=0;function sV(e,t,i,s){let{latestValues:r}=t;r[e]&&(i[e]=r[e],t.setStaticValue(e,0),s&&(s[e]=0))}function sI({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(e={},i=t?.()){this.id=sD++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(sk.nodes=sk.calculatedTargetDeltas=sk.calculatedProjections=0),this.nodes.forEach(sB),this.nodes.forEach(sH),this.nodes.forEach(sW),this.nodes.forEach(sF),c.addProjectionMetrics&&c.addProjectionMetrics(sk)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ss)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new T),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=st(t)&&!(st(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),e){let i,s=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=P.now(),s=({timestamp:t})=>{let r=t-i;r>=250&&(f(s),e(r-250))};return m.setup(s,!0),()=>f(s)}(s,250),i3.hasAnimatedSinceResize&&(i3.hasAnimatedSinceResize=!1,this.nodes.forEach(sG))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||sZ,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=r.getProps(),d=!this.targetLayout||!sP(this.targetLayout,s),u=!t&&i;if(this.options.layoutRoot||this.resumeFrom||u||t&&(d||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(n,"layout"),onPlay:a,onComplete:o};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||sG(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sU),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let s=i.props[V];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",m,!(e||i))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s$);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sz);this.isUpdating||this.nodes.forEach(sz),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(sN),this.nodes.forEach(sR),this.nodes.forEach(sj),this.clearAllSnapshots();let e=P.now();g.delta=j(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iQ.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s_),this.sharedNodes.forEach(sq)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iu(this.snapshot.measuredBox.x)||iu(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ib(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=s(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!sS(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;e&&this.instance&&(t||iE(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),s=this.removeElementScroll(i);return e&&(s=this.removeTransform(s)),s0((t=s).x),s0(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ib();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(s2))){let{scroll:e}=this.root;e&&(ik(t.x,e.offset.x),ik(t.y,e.offset.y))}return t}removeElementScroll(e){let t=ib();if(sm(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sm(t,e),ik(t.x,r.offset.x),ik(t.y,r.offset.y))}return t}applyTransform(e,t=!1){let i=ib();sm(i,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];!t&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iO(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iE(s.latestValues)&&iO(i,s.latestValues)}return iE(this.latestValues)&&iO(i,this.latestValues),i}removeTransform(e){let t=ib();sm(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iE(i.latestValues))continue;iT(i.latestValues)&&i.updateSnapshot();let s=ib();sm(s,i.measurePageBox()),sb(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iE(this.latestValues)&&sb(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ib(),this.relativeTargetOrigin=ib(),ig(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),sm(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ib(),this.targetWithTransforms=ib()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,a,o;this.forceRelativeParentToResolveTarget(),n=this.target,a=this.relativeTarget,o=this.relativeParent.target,ip(n.x,a.x,o.x),ip(n.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sm(this.target,this.layout.layoutBox),iA(this.target,this.targetDelta)):sm(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ib(),this.relativeTargetOrigin=ib(),ig(this.relativeTargetOrigin,this.target,e.target),sm(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&sk.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iT(this.parent.latestValues)||iP(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;sm(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;!function(e,t,i,s=!1){let r,n,a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iO(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,iA(e,n)),s&&iE(r.latestValues)&&iO(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=ib());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sf(this.prevProjectionDelta.x,this.projectionDelta.x),sf(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ic(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&sC(this.projectionDelta.x,this.prevProjectionDelta.x)&&sC(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),c.value&&sk.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iy(),this.projectionDelta=iy(),this.projectionDeltaWithTransform=iy()}setAnimationOrigin(e,t=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=iy();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=ib(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),u=!d||d.members.length<=1,h=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(sK));this.animationProgress=0,this.mixTargetDelta=t=>{let s=t/1e3;if(sY(a.x,e.x,s),sY(a.y,e.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,c,p,m,f,g;ig(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=s,sX(p.x,m.x,f.x,g),sX(p.y,m.y,f.y,g),i&&(d=this.relativeTarget,c=i,sT(d.x,c.x)&&sT(d.y,c.y))&&(this.isProjectionDirty=!1),i||(i=ib()),sm(i,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,i,s,r,n){r?(e.opacity=eT(0,i.opacity??1,su(s)),e.opacityExit=eT(t.opacity??1,0,sh(s))):n&&(e.opacity=eT(t.opacity??1,i.opacity??1,s));for(let r=0;r<sa;r++){let n=`border${sn[r]}Radius`,a=sd(t,n),o=sd(i,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sl(a)===sl(o)?(e[n]=Math.max(eT(so(a),so(o),s),0),(en.test(o)||en.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||i.rotate)&&(e.rotate=eT(t.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{i3.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=A(0)),this.currentAnimation=function(e,t,i){let s=L(e)?e:A(e);return s.start(tK("",s,t,i)),s.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:s,latestValues:r}=e;if(t&&i&&s){if(this!==e&&this.layout&&s&&s1(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||ib();let t=iu(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let s=iu(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+s}sm(t,i),iO(t,r),ic(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new sA),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let s={};i.z&&sV("z",e,s,this.animationValues);for(let t=0;t<sL.length;t++)sV(`rotate${sL[t]}`,e,s,this.animationValues),sV(`skew${sL[t]}`,e,s,this.animationValues);for(let t in e.render(),s)e.setStaticValue(t,s[t]),this.animationValues&&(this.animationValues[t]=s[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sO;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=sr(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=sr(e?.pointerEvents)||""),this.hasProjected&&!iE(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let s="",r=e.x.translate/t.x,n=e.y.translate/t.y,a=i?.z||0;if((r||n||a)&&(s=`translate3d(${r}px, ${n}px, ${a}px) `),(1!==t.x||1!==t.y)&&(s+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:n,skewX:a,skewY:o}=i;e&&(s=`perspective(${e}px) ${s}`),t&&(s+=`rotate(${t}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(t.transform=i(r,t.transform));let{x:n,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,s.animationValues?t.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,i8){if(void 0===r[e])continue;let{correct:i,applyTo:n,isCSSVariable:a}=i8[e],o="none"===t.transform?r[e]:i(r[e],s);if(n){let e=n.length;for(let i=0;i<e;i++)t[n[i]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=s===this?sr(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(s$),this.root.sharedNodes.clear()}}}function sR(e){e.updateLayout()}function sj(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=e.layout,{animationType:r}=e.options,n=t.source!==e.layout.source;"size"===r?ix(e=>{let s=n?t.measuredBox[e]:t.layoutBox[e],r=iu(s);s.min=i[e].min,s.max=s.min+r}):s1(r,t.layoutBox,i)&&ix(s=>{let r=n?t.measuredBox[s]:t.layoutBox[s],a=iu(i[s]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[s].max=e.relativeTarget[s].min+a)});let a=iy();ic(a,i,t.layoutBox);let o=iy();n?ic(o,e.applyTransform(s,!0),t.measuredBox):ic(o,i,t.layoutBox);let l=!sS(a),d=!1;if(!e.resumeFrom){let s=e.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let a=ib();ig(a,t.layoutBox,r.layoutBox);let o=ib();ig(o,i,n.layoutBox),sP(a,o)||(d=!0),s.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=s)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function sB(e){c.value&&sk.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function sF(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function s_(e){e.clearSnapshot()}function s$(e){e.clearMeasurements()}function sz(e){e.isLayoutDirty=!1}function sN(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function sG(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function sH(e){e.resolveTargetDelta()}function sW(e){e.calcProjection()}function sU(e){e.resetSkewAndRotation()}function sq(e){e.removeLeadSnapshot()}function sY(e,t,i){e.translate=eT(t.translate,0,i),e.scale=eT(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function sX(e,t,i,s){e.min=eT(t.min,i.min,s),e.max=eT(t.max,i.max,s)}function sK(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let sZ={duration:.45,ease:[.4,0,.1,1]},sQ=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),sJ=sQ("applewebkit/")&&!sQ("chrome/")?Math.round:d;function s0(e){e.min=sJ(e.min),e.max=sJ(e.max)}function s1(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(sM(t)-sM(i)))}function s2(e){return e!==e.root&&e.scroll?.wasRoot}let s3=sI({attachResizeListener:(e,t)=>ir(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),s5={current:void 0},s4=sI({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!s5.current){let e=new s3({});e.mount(window),e.setOptions({layoutScroll:!0}),s5.current=e}return s5.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});var s8=i(373);function s9(e,t){let i=(0,s8.K)(e),s=new AbortController;return[i,{passive:!0,...t,signal:s.signal},()=>s.abort()]}function s6(e){return!("touch"===e.pointerType||is.x||is.y)}function s7(e,t,i){let{props:s}=e;e.animationState&&s.whileHover&&e.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&m.postRender(()=>r(t,io(t)))}class re extends t7{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=s9(e,i),a=e=>{if(!s6(e))return;let{target:i}=e,s=t(i,e);if("function"!=typeof s||!i)return;let n=e=>{s6(e)&&(s(e),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,r)};return s.forEach(e=>{e.addEventListener("pointerenter",a,r)}),n}(e,(e,t)=>(s7(this.node,t,"Start"),e=>s7(this.node,e,"End"))))}unmount(){}}class rt extends t7{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=R(ir(this.node.current,"focus",()=>this.onFocus()),ir(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ri=(e,t)=>!!t&&(e===t||ri(e,t.parentElement)),rs=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rr=new WeakSet;function rn(e){return t=>{"Enter"===t.key&&e(t)}}function ra(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function ro(e){return ia(e)&&!(is.x||is.y)}function rl(e,t,i){let{props:s}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&s.whileTap&&e.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(t,io(t)))}class rd extends t7{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[s,r,n]=s9(e,i),a=e=>{let s=e.currentTarget;if(!ro(e))return;rr.add(s);let n=t(s,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rr.has(s)&&rr.delete(s),ro(e)&&"function"==typeof n&&n(e,{success:t})},o=e=>{a(e,s===window||s===document||i.useGlobalTarget||ri(s,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return s.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,r),(0,tN.s)(e))&&(e.addEventListener("focus",e=>((e,t)=>{let i=e.currentTarget;if(!i)return;let s=rn(()=>{if(rr.has(i))return;ra(i,"down");let e=rn(()=>{ra(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>ra(i,"cancel"),t)});i.addEventListener("keydown",s,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),t)})(e,r)),rs.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),n}(e,(e,t)=>(rl(this.node,t,"Start"),(e,{success:t})=>rl(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ru=new WeakMap,rh=new WeakMap,rc=e=>{let t=ru.get(e.target);t&&t(e)},rp=e=>{e.forEach(rc)},rm={some:0,all:1};class rf extends t7{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:s="some",once:r}=e,n={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rm[s]};return function(e,t,i){let s=function({root:e,...t}){let i=e||document;rh.has(i)||rh.set(i,{});let s=rh.get(i),r=JSON.stringify(t);return s[r]||(s[r]=new IntersectionObserver(rp,{root:e,...t})),s[r]}(t);return ru.set(e,i),s.observe(e),()=>{ru.delete(e),s.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=t?i:s;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let rg=(0,iJ.createContext)({strict:!1});var rv=i(9751);let ry=(0,iJ.createContext)({});function rw(e){return r(e.animate)||t3.some(t=>t1(e[t]))}function rb(e){return!!(rw(e)||e.variants)}function rx(e){return Array.isArray(e)?e.join(" "):e}var rS=i(2205);let rT={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rE={};for(let e in rT)rE[e]={isEnabled:t=>rT[e].some(e=>!!t[e])};let rP=Symbol.for("motionComponentSymbol");var rM=i(3866),rC=i(181);function rA(e,{layout:t,layoutId:i}){return w.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!i8[e]||"opacity"===e)}let rk=(e,t)=>t&&"number"==typeof e?t.transform(e):e,rL={...U,transform:Math.round},rO={borderWidth:ea,borderTopWidth:ea,borderRightWidth:ea,borderBottomWidth:ea,borderLeftWidth:ea,borderRadius:ea,radius:ea,borderTopLeftRadius:ea,borderTopRightRadius:ea,borderBottomRightRadius:ea,borderBottomLeftRadius:ea,width:ea,maxWidth:ea,height:ea,maxHeight:ea,top:ea,right:ea,bottom:ea,left:ea,padding:ea,paddingTop:ea,paddingRight:ea,paddingBottom:ea,paddingLeft:ea,margin:ea,marginTop:ea,marginRight:ea,marginBottom:ea,marginLeft:ea,backgroundPositionX:ea,backgroundPositionY:ea,rotate:er,rotateX:er,rotateY:er,rotateZ:er,scale:Y,scaleX:Y,scaleY:Y,scaleZ:Y,skew:er,skewX:er,skewY:er,distance:ea,translateX:ea,translateY:ea,translateZ:ea,x:ea,y:ea,z:ea,perspective:ea,transformPerspective:ea,opacity:q,originX:ed,originY:ed,originZ:ea,zIndex:rL,fillOpacity:q,strokeOpacity:q,numOctaves:rL},rD={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rV=y.length;function rI(e,t,i){let{style:s,vars:r,transformOrigin:n}=e,a=!1,o=!1;for(let e in t){let i=t[e];if(w.has(e)){a=!0;continue}if(N(e)){r[e]=i;continue}{let t=rk(i,rO[e]);e.startsWith("origin")?(o=!0,n[e]=t):s[e]=t}}if(!t.transform&&(a||i?s.transform=function(e,t,i){let s="",r=!0;for(let n=0;n<rV;n++){let a=y[n],o=e[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let e=rk(o,rO[a]);if(!l){r=!1;let t=rD[a]||a;s+=`${t}(${e}) `}i&&(t[a]=e)}}return s=s.trim(),i?s=i(t,r?"":s):r&&(s="none"),s}(t,e.transform,i):s.transform&&(s.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=n;s.transformOrigin=`${e} ${t} ${i}`}}let rR=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rj(e,t,i){for(let s in t)L(t[s])||rA(s,i)||(e[s]=t[s])}let rB={offset:"stroke-dashoffset",array:"stroke-dasharray"},rF={offset:"strokeDashoffset",array:"strokeDasharray"};function r_(e,{attrX:t,attrY:i,attrScale:s,pathLength:r,pathSpacing:n=1,pathOffset:a=0,...o},l,d,u){if(rI(e,o,d),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:c}=e;h.transform&&(c.transform=h.transform,delete h.transform),(c.transform||h.transformOrigin)&&(c.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==i&&(h.y=i),void 0!==s&&(h.scale=s),void 0!==r&&function(e,t,i=1,s=0,r=!0){e.pathLength=1;let n=r?rB:rF;e[n.offset]=ea.transform(-s);let a=ea.transform(t),o=ea.transform(i);e[n.array]=`${a} ${o}`}(h,r,n,a,!1)}let r$=()=>({...rR(),attrs:{}}),rz=e=>"string"==typeof e&&"svg"===e.toLowerCase(),rN=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rG(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rN.has(e)}let rH=e=>!rG(e);try{!function(e){"function"==typeof e&&(rH=t=>t.startsWith("on")?!rG(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let rW=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rU(e){if("string"!=typeof e||e.includes("-"));else if(rW.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var rq=i(1200);let rY=e=>(t,i)=>{let s=(0,iJ.useContext)(ry),n=(0,iJ.useContext)(rM.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,s,n){return{latestValues:function(e,t,i,s){let n={},o=s(e,{});for(let e in o)n[e]=sr(o[e]);let{initial:l,animate:d}=e,u=rw(e),h=rb(e);t&&h&&!u&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===d&&(d=t.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?d:l;if(p&&"boolean"!=typeof p&&!r(p)){let t=Array.isArray(p)?p:[p];for(let i=0;i<t.length;i++){let s=a(e,t[i]);if(s){let{transitionEnd:e,transition:t,...i}=s;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(i,s,n,e),renderState:t()}})(e,t,s,n);return i?o():(0,rq.M)(o)};function rX(e,t,i){let{style:s}=e,r={};for(let n in s)(L(s[n])||t.style&&L(t.style[n])||rA(n,e)||i?.getValue(n)?.liveStyle!==void 0)&&(r[n]=s[n]);return r}let rK={useVisualState:rY({scrapeMotionValuesFromProps:rX,createRenderState:rR})};function rZ(e,t,i){let s=rX(e,t,i);for(let i in e)(L(e[i])||L(t[i]))&&(s[-1!==y.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return s}let rQ={useVisualState:rY({scrapeMotionValuesFromProps:rZ,createRenderState:r$})},rJ=e=>t=>t.test(e),r0=[U,ea,en,er,el,eo,{test:e=>"auto"===e,parse:e=>e}],r1=e=>r0.find(rJ(e)),r2=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),r3=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,r5=e=>/^0[^.\s]+$/u.test(e),r4=new Set(["brightness","contrast","saturate","opacity"]);function r8(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[s]=i.match(K)||[];if(!s)return e;let r=i.replace(s,""),n=+!!r4.has(t);return s!==i&&(n*=100),t+"("+n+r+")"}let r9=/\b([a-z-]*)\(.*?\)/gu,r6={...eb,getAnimatableNone:e=>{let t=e.match(r9);return t?t.map(r8).join(" "):e}},r7={...rO,color:eh,backgroundColor:eh,outlineColor:eh,fill:eh,stroke:eh,borderColor:eh,borderTopColor:eh,borderRightColor:eh,borderBottomColor:eh,borderLeftColor:eh,filter:r6,WebkitFilter:r6},ne=e=>r7[e];function nt(e,t){let i=ne(e);return i!==r6&&(i=eb),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let ni=new Set(["auto","none","0"]);class ns extends tA{constructor(e,t,i,s,r){super(e,t,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let s=e[i];if("string"==typeof s&&H(s=s.trim())){let r=function e(t,i,s=1){$(s<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=function(e){let t=r3.exec(e);if(!t)return[,];let[,i,s,r]=t;return[`--${i??s}`,r]}(t);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let e=a.trim();return r2(e)?parseFloat(e):e}return H(n)?e(n,i,s+1):n}(s,t.current);void 0!==r&&(e[i]=r),i===e.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!b.has(i)||2!==e.length)return;let[s,r]=e,n=r1(s),a=r1(r);if(n!==a)if(ty(n)&&ty(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else tx[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var s;(null===e[t]||("number"==typeof(s=e[t])?0===s:null===s||"none"===s||"0"===s||r5(s)))&&i.push(t)}i.length&&function(e,t,i){let s,r=0;for(;r<e.length&&!s;){let t=e[r];"string"==typeof t&&!ni.has(t)&&eg(t).values.length&&(s=e[r]),r++}if(s&&i)for(let r of t)e[r]=nt(i,s)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tx[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let s=t[t.length-1];void 0!==s&&e.getValue(i,s).jump(s,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let s=e.getValue(t);s&&s.jump(this.measuredOrigin,!1);let r=i.length-1,n=i[r];i[r]=tx[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let nr=[...r0,eh,eb],nn={current:null},na={current:!1},no=new WeakMap,nl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nd{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tA,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=P.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,m.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=rw(t),this.isVariantNode=rb(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:d,...u}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in u){let t=u[e];void 0!==o[e]&&L(t)&&t.set(o[e],!1)}}mount(e){this.current=e,no.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),na.current||function(){if(na.current=!0,rS.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nn.current=e.matches;e.addListener(t),t()}else nn.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nn.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let s=w.has(e);s&&this.onBindTransform&&this.onBindTransform();let r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&m.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),n(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rE){let t=rE[e];if(!t)continue;let{isEnabled:i,Feature:s}=t;if(!this.features[e]&&s&&i(this.props)&&(this.features[e]=new s(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ib()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nl.length;t++){let i=nl[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=e["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(e,t,i){for(let s in t){let r=t[s],n=i[s];if(L(r))e.addValue(s,r);else if(L(n))e.addValue(s,A(r,{owner:e}));else if(n!==r)if(e.hasValue(s)){let t=e.getValue(s);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(s);e.addValue(s,A(void 0!==t?t:r,{owner:e}))}}for(let s in i)void 0===t[s]&&e.removeValue(s);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=A(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=i){if("string"==typeof i&&(r2(i)||r5(i)))i=parseFloat(i);else{let s;s=i,!nr.find(rJ(s))&&eb.test(t)&&(i=nt(e,t))}this.setBaseTarget(e,L(i)?i.get():i)}return L(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=a(this.props,i,this.presenceContext?.custom);s&&(t=s[e])}if(i&&void 0!==t)return t;let s=this.getBaseTargetFromProps(this.props,e);return void 0===s||L(s)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:s}on(e,t){return this.events[e]||(this.events[e]=new T),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class nu extends nd{constructor(){super(...arguments),this.KeyframeResolver=ns}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function nh(e,{style:t,vars:i},s,r){for(let n in Object.assign(e.style,t,r&&r.getProjectionStyles(s)),i)e.style.setProperty(n,i[n])}class nc extends nu{constructor(){super(...arguments),this.type="html",this.renderInstance=nh}readValueFromInstance(e,t){if(w.has(t))return this.projection?.isProjecting?tf(t):((e,t)=>{let{transform:i="none"}=getComputedStyle(e);return tg(i,t)})(e,t);{let i=window.getComputedStyle(e),s=(N(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iD(e,t)}build(e,t,i){rI(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return rX(e,t,i)}}let np=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nm extends nu{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ib}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(w.has(t)){let e=ne(t);return e&&e.default||0}return t=np.has(t)?t:D(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return rZ(e,t,i)}build(e,t,i){r_(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,s){for(let i in nh(e,t,void 0,s),t.attrs)e.setAttribute(np.has(i)?i:D(i),t.attrs[i])}mount(e){this.isSVGTag=rz(e.tagName),super.mount(e)}}let nf=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,s)=>"create"===s?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}((t$={animation:{Feature:ie},exit:{Feature:ii},inView:{Feature:rf},tap:{Feature:rd},focus:{Feature:rt},hover:{Feature:re},pan:{Feature:iK},drag:{Feature:iY,ProjectionNode:s4,MeasureLayout:i6},layout:{ProjectionNode:s4,MeasureLayout:i6}},tz=(e,t)=>rU(e)?new nm(t):new nc(t,{allowProjection:e!==iJ.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:s,Component:r}){function n(e,n){var a;let o,l={...(0,iJ.useContext)(rv.Q),...e,layoutId:function({layoutId:e}){let t=(0,iJ.useContext)(i1.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=l,u=function(e){let{initial:t,animate:i}=function(e,t){if(rw(e)){let{initial:t,animate:i}=e;return{initial:!1===t||t1(t)?t:void 0,animate:t1(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,iJ.useContext)(ry));return(0,iJ.useMemo)(()=>({initial:t,animate:i}),[rx(t),rx(i)])}(e),h=s(e,d);if(!d&&rS.B){(0,iJ.useContext)(rg).strict;let e=function(e){let{drag:t,layout:i}=rE;if(!t&&!i)return{};let s={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(l);o=e.MeasureLayout,u.visualElement=function(e,t,i,s,r){let{visualElement:n}=(0,iJ.useContext)(ry),a=(0,iJ.useContext)(rg),o=(0,iJ.useContext)(rM.t),l=(0,iJ.useContext)(rv.Q).reducedMotion,d=(0,iJ.useRef)(null);s=s||a.renderer,!d.current&&s&&(d.current=s(e,{visualState:t,parent:n,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=d.current,h=(0,iJ.useContext)(i2);u&&!u.projection&&r&&("html"===u.type||"svg"===u.type)&&function(e,t,i,s){let{layoutId:r,layout:n,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:d,layoutCrossfade:u}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!a||o&&iI(o),visualElement:e,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:d})}(d.current,i,r,h);let c=(0,iJ.useRef)(!1);(0,iJ.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let p=i[V],m=(0,iJ.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rC.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),iQ.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,iJ.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(r,h,l,t,e.ProjectionNode)}return(0,iZ.jsxs)(ry.Provider,{value:u,children:[o&&u.visualElement?(0,iZ.jsx)(o,{visualElement:u.visualElement,...l}):null,i(r,e,(a=u.visualElement,(0,iJ.useCallback)(e=>{e&&h.onMount&&h.onMount(e),a&&(e?a.mount(e):a.unmount()),n&&("function"==typeof n?n(e):iI(n)&&(n.current=e))},[a])),h,d,u.visualElement)]})}e&&function(e){for(let t in e)rE[t]={...rE[t],...e[t]}}(e),n.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let a=(0,iJ.forwardRef)(n);return a[rP]=r,a}({...rU(e)?rQ:rK,preloadedFeatures:t$,useRender:function(e=!1){return(t,i,s,{latestValues:r},n)=>{let a=(rU(t)?function(e,t,i,s){let r=(0,iJ.useMemo)(()=>{let i=r$();return r_(i,t,rz(s),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rj(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let i={},s=function(e,t){let i=e.style||{},s={};return rj(s,i,e),Object.assign(s,function({transformTemplate:e},t){return(0,iJ.useMemo)(()=>{let i=rR();return rI(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),s}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,t),o=function(e,t,i){let s={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(rH(r)||!0===i&&rG(r)||!t&&!rG(r)||e.draggable&&r.startsWith("onDrag"))&&(s[r]=e[r]);return s}(i,"string"==typeof t,e),l=t!==iJ.Fragment?{...o,...a,ref:s}:{},{children:d}=i,u=(0,iJ.useMemo)(()=>L(d)?d.get():d,[d]);return(0,iJ.createElement)(t,{...l,children:u})}}(t),createVisualElement:tz,Component:e})}))},9751:(e,t,i)=>{"use strict";i.d(t,{Q:()=>s});let s=(0,i(4232).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})}}]);