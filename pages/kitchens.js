import { useRef, useState, useEffect } from 'react';
import Head from 'next/head';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, EffectCoverflow, Autoplay, Thumbs } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getKitchensData, getFooterData } from '../database/api-client.js';
import { getImageURL } from '../src/config/api.js';
import Navbar from '../src/components/Navbar';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';
import 'swiper/css/thumbs';

const KitchensPage = () => {
  const sectionRef = useRef(null);
  const [thumbsSwiper, setThumbsSwiper] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [kitchens, setKitchens] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [socialMedia, setSocialMedia] = useState([]);

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadKitchensData = async () => {
      try {
        const kitchensData = await getKitchensData();
        console.log('Loaded kitchens data:', kitchensData);
        if (kitchensData && kitchensData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedKitchens = kitchensData.map(kitchen => ({
            id: kitchen.id,
            title: kitchen.title,
            description: kitchen.description,
            // إذا لم تكن هناك فئة محددة، استخدم 'modern-kitchens' كافتراضي
            category: kitchen.category_slug || 'modern-kitchens',
            images: kitchen.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          console.log('Formatted kitchens:', formattedKitchens);
          setKitchens(formattedKitchens);
        } else {
          console.log('No kitchens data found, using fallback data');
          // استخدام بيانات احتياطية إذا لم تكن هناك بيانات في قاعدة البيانات
          setKitchens(fallbackKitchens);
        }
      } catch (error) {
        console.error('Error loading kitchens data:', error);
        // استخدام بيانات احتياطية في حالة الخطأ
        setKitchens(fallbackKitchens);
      }
    };

    const loadFooterData = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData) {
          setWhatsappNumber(footerData.whatsapp_number || '966501234567');
          setSocialMedia(footerData.social_media || []);
        }
      } catch (error) {
        console.error('Error loading footer data:', error);
        setWhatsappNumber('966501234567');
        setSocialMedia([]);
      }
    };

    loadKitchensData();
    loadFooterData();
  }, []);

  // بيانات احتياطية للمطابخ
  const fallbackKitchens = [
    {
      id: 1,
      title: 'مطبخ عصري فاخر',
      description: 'تصميم مطبخ عصري بأحدث التقنيات والمواد عالية الجودة',
      category: 'modern-kitchens',
      images: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800'
      ]
    },
    // يمكن إضافة المزيد من البيانات الاحتياطية هنا
  ];

  return (
    <>
      <Head>
        <title>معرض المطابخ العصرية والكلاسيكية - عجائب الخبراء</title>
        <meta name="description" content="استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية والفاخرة والاسكندنافية والمينيمال. أحدث تصاميم المطابخ في السعودية مع عجائب الخبراء" />
        <meta name="keywords" content="مطابخ عصرية, مطابخ كلاسيكية, مطابخ فاخرة, مطابخ اسكندنافية, مطابخ مينيمال, تصاميم مطابخ, معرض مطابخ, عجائب الخبراء" />
        <link rel="canonical" href="https://khobrakitchens.com/kitchens" />
        <meta property="og:title" content="معرض المطابخ العصرية والكلاسيكية - عجائب الخبراء" />
        <meta property="og:description" content="استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية والفاخرة والاسكندنافية والمينيمال." />
        <meta property="og:url" content="https://khobrakitchens.com/kitchens" />
        <meta property="og:type" content="website" />
      </Head>

      <div className="bg-gray-50">
        <Navbar />
        {/* سيتم إضافة باقي المحتوى هنا */}
        <div className="min-h-screen pt-20">
          <div className="container mx-auto px-4 py-8">
            <h1 className="text-4xl font-bold text-center mb-8">معرض المطابخ</h1>
            <p className="text-center text-gray-600 mb-12">
              استكشف مجموعتنا المتنوعة من تصاميم المطابخ العصرية والكلاسيكية
            </p>
            
            {/* معرض المطابخ مع إمكانية فتح الموديل */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {kitchens.map((kitchen) => (
                <div
                  key={kitchen.id}
                  className="bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
                  onClick={() => {
                    setLightboxImage(kitchen);
                    setModalImageIndex(0);
                  }}
                >
                  {kitchen.images && kitchen.images.length > 0 && (
                    <img
                      src={kitchen.images[0]}
                      alt={kitchen.title}
                      className="w-full h-64 object-cover"
                    />
                  )}
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2">{kitchen.title}</h3>
                    <p className="text-gray-600 mb-4">{kitchen.description}</p>
                    <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* موديل عرض تفاصيل المطبخ */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
            onClick={() => setLightboxImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex flex-col md:flex-row">
                {/* قسم الصور */}
                <div className="md:w-2/3">
                  {lightboxImage.images && lightboxImage.images.length > 0 && (
                    <div className="relative">
                      <Swiper
                        modules={[Navigation, Pagination, Thumbs]}
                        navigation
                        pagination={{ clickable: true }}
                        thumbs={{ swiper: thumbsSwiper }}
                        className="h-96 md:h-[500px]"
                      >
                        {lightboxImage.images.map((image, index) => (
                          <SwiperSlide key={index}>
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </SwiperSlide>
                        ))}
                      </Swiper>

                      {/* صور مصغرة */}
                      {lightboxImage.images.length > 1 && (
                        <Swiper
                          modules={[Thumbs]}
                          onSwiper={setThumbsSwiper}
                          spaceBetween={10}
                          slidesPerView={4}
                          watchSlidesProgress
                          className="mt-2 h-20"
                        >
                          {lightboxImage.images.map((image, index) => (
                            <SwiperSlide key={index}>
                              <img
                                src={image}
                                alt={`صورة مصغرة ${index + 1}`}
                                className="w-full h-full object-cover rounded cursor-pointer"
                              />
                            </SwiperSlide>
                          ))}
                        </Swiper>
                      )}
                    </div>
                  )}
                </div>

                {/* قسم التفاصيل */}
                <div className="md:w-1/3 p-6 overflow-y-auto">
                  <div className="flex justify-between items-start mb-4">
                    <h2 className="text-2xl font-bold text-gray-800">{lightboxImage.title}</h2>
                    <button
                      onClick={() => setLightboxImage(null)}
                      className="text-gray-500 hover:text-gray-700 text-2xl"
                    >
                      ×
                    </button>
                  </div>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {lightboxImage.description}
                  </p>

                  {/* قسم المميزات */}
                  <div className="mb-6">
                    <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                      <i className="ri-star-line text-yellow-500 ml-2"></i>
                      المميزات
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>تصميم عصري وأنيق</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>مواد عالية الجودة</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>ضمان 5 سنوات</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>تركيب مجاني</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>خدمة ما بعد البيع</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <i className="ri-check-line text-green-500 ml-2"></i>
                        <span>تصميم حسب المقاس</span>
                      </div>
                    </div>
                  </div>

                  {/* وسائل التواصل الاجتماعي */}
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                      <i className="ri-share-line text-blue-500 ml-2"></i>
                      تابعنا على
                    </h4>
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <a
                        href="https://wa.me/966557611105"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-whatsapp-line text-xl"></i>
                        <span className="text-xs">واتساب</span>
                      </a>

                      <a
                        href="https://instagram.com/koprakatchn"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-instagram-line text-xl"></i>
                        <span className="text-xs">انستجرام</span>
                      </a>

                      <a
                        href="https://twitter.com/khobrakitchens"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-blue-400 hover:bg-blue-500 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-twitter-line text-xl"></i>
                        <span className="text-xs">تويتر</span>
                      </a>

                      <a
                        href="https://snapchat.com/add/khobrakitchens"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-yellow-400 hover:bg-yellow-500 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-snapchat-line text-xl"></i>
                        <span className="text-xs">سناب شات</span>
                      </a>

                      <a
                        href="https://tiktok.com/@khobrakitchens"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-black hover:bg-gray-800 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-tiktok-line text-xl"></i>
                        <span className="text-xs">تيك توك</span>
                      </a>

                      <button
                        onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                        className="bg-primary hover:bg-blue-600 text-white p-3 rounded-lg flex flex-col items-center gap-1 transition-colors"
                      >
                        <i className="ri-phone-line text-xl"></i>
                        <span className="text-xs">اتصل بنا</span>
                      </button>
                    </div>
                  </div>

                  {/* زر واتساب مميز */}
                  <div>
                    <a
                      href={`https://wa.me/${whatsappNumber}?text=مرحباً، أريد الاستفسار عن ${lightboxImage.title}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-green-500 hover:bg-green-600 text-white px-6 py-4 rounded-lg flex items-center justify-center gap-2 transition-colors font-bold text-lg"
                    >
                      <i className="ri-whatsapp-line text-2xl"></i>
                      استفسر الآن عبر واتساب
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
};

export default KitchensPage;
