import { useState, useCallback } from 'react'

// Hook لتحسين رفع الصور
export const useImageOptimization = () => {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)

  // ضغط الصورة قبل الرفع
  const compressImage = useCallback((file, quality = 0.8) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // حساب الأبعاد الجديدة
        let { width, height } = img
        const maxWidth = 1920
        const maxHeight = 1080

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height

        // رسم الصورة المضغوطة
        ctx.drawImage(img, 0, 0, width, height)

        canvas.toBlob(resolve, file.type, quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }, [])

  // رفع الصورة مع التحسين
  const uploadOptimizedImage = useCallback(async (file, endpoint, options = {}) => {
    const { compress = true, quality = 0.8, onProgress } = options

    try {
      setUploading(true)
      setProgress(0)

      // التحقق من حجم الملف
      if (file.size > 2 * 1024 * 1024) {
        throw new Error('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت')
      }

      // التحقق من نوع الملف
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        throw new Error('نوع الملف غير مدعوم. يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)')
      }

      // ضغط الصورة إذا كان مطلوباً
      let fileToUpload = file
      if (compress && file.size > 500 * 1024) { // ضغط الملفات أكبر من 500KB
        setProgress(25)
        fileToUpload = await compressImage(file, quality)
      }

      setProgress(50)

      // إنشاء FormData
      const formData = new FormData()
      formData.append('image', fileToUpload)

      // رفع الصورة
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData
      })

      setProgress(75)

      const result = await response.json()

      if (response.ok && result.success) {
        setProgress(100)
        
        if (onProgress) {
          onProgress(100)
        }

        return {
          success: true,
          imagePath: result.imagePath,
          originalSize: file.size,
          compressedSize: fileToUpload.size,
          compressionRatio: ((file.size - fileToUpload.size) / file.size * 100).toFixed(1)
        }
      } else {
        throw new Error(result.error || 'فشل في رفع الصورة')
      }
    } catch (error) {
      console.error('خطأ في رفع الصورة:', error)
      throw error
    } finally {
      setUploading(false)
      setTimeout(() => setProgress(0), 1000)
    }
  }, [compressImage])

  return {
    uploading,
    progress,
    uploadOptimizedImage,
    compressImage
  }
}

// Hook لإدارة الصور المتعددة
export const useMultipleImages = (maxImages = 5) => {
  const [images, setImages] = useState([])
  const [uploadingIndex, setUploadingIndex] = useState(null)

  const addImage = useCallback((imagePath) => {
    setImages(prev => {
      if (prev.length >= maxImages) {
        return [...prev.slice(1), imagePath]
      }
      return [...prev, imagePath]
    })
  }, [maxImages])

  const removeImage = useCallback((index) => {
    setImages(prev => prev.filter((_, i) => i !== index))
  }, [])

  const updateImage = useCallback((index, imagePath) => {
    setImages(prev => prev.map((img, i) => i === index ? imagePath : img))
  }, [])

  const reorderImages = useCallback((fromIndex, toIndex) => {
    setImages(prev => {
      const newImages = [...prev]
      const [removed] = newImages.splice(fromIndex, 1)
      newImages.splice(toIndex, 0, removed)
      return newImages
    })
  }, [])

  return {
    images,
    setImages,
    addImage,
    removeImage,
    updateImage,
    reorderImages,
    uploadingIndex,
    setUploadingIndex,
    canAddMore: images.length < maxImages
  }
}

// Hook للتحقق من صحة الصور
export const useImageValidation = () => {
  const validateImage = useCallback((file) => {
    const errors = []

    // التحقق من وجود الملف
    if (!file) {
      errors.push('لم يتم اختيار ملف')
      return { isValid: false, errors }
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      errors.push('نوع الملف غير مدعوم. يُسمح برفع الصور فقط (JPEG, PNG, WebP, GIF)')
    }

    // التحقق من حجم الملف
    const maxSize = 2 * 1024 * 1024 // 2MB
    if (file.size > maxSize) {
      errors.push('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت')
    }

    // التحقق من أبعاد الصورة (اختياري)
    return new Promise((resolve) => {
      if (errors.length > 0) {
        resolve({ isValid: false, errors })
        return
      }

      const img = new Image()
      img.onload = () => {
        // يمكن إضافة قيود على الأبعاد هنا
        const minWidth = 100
        const minHeight = 100
        const maxWidth = 4000
        const maxHeight = 4000

        if (img.width < minWidth || img.height < minHeight) {
          errors.push(`أبعاد الصورة صغيرة جداً. الحد الأدنى ${minWidth}x${minHeight} بكسل`)
        }

        if (img.width > maxWidth || img.height > maxHeight) {
          errors.push(`أبعاد الصورة كبيرة جداً. الحد الأقصى ${maxWidth}x${maxHeight} بكسل`)
        }

        resolve({
          isValid: errors.length === 0,
          errors,
          dimensions: {
            width: img.width,
            height: img.height,
            aspectRatio: (img.width / img.height).toFixed(2)
          }
        })
      }

      img.onerror = () => {
        errors.push('فشل في قراءة الصورة. تأكد من أن الملف صورة صحيحة')
        resolve({ isValid: false, errors })
      }

      img.src = URL.createObjectURL(file)
    })
  }, [])

  return { validateImage }
}
