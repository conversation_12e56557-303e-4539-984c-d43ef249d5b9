import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import MobileProductModal from './MobileProductModal';

const MobileHome = () => {
  const [heroData, setHeroData] = useState(null);
  const [kitchens, setKitchens] = useState([]);
  const [cabinets, setCabinets] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedProductType, setSelectedProductType] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch hero data
      const heroResponse = await fetch('/api/hero');
      const heroResult = await heroResponse.json();
      setHeroData(heroResult);

      // Fetch kitchens (first 4)
      const kitchensResponse = await fetch('/api/kitchens');
      const kitchensResult = await kitchensResponse.json();
      setKitchens(kitchensResult.slice(0, 4));

      // Fetch cabinets (first 4)
      const cabinetsResponse = await fetch('/api/cabinets');
      const cabinetsResult = await cabinetsResponse.json();
      setCabinets(cabinetsResult.slice(0, 4));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const openWhatsApp = () => {
    const message = "مرحباً، أنا مهتم بخدماتكم في تصميم وتنفيذ المطابخ والخزائن";
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      {heroData && (
        <motion.section
          className="relative h-72 overflow-hidden"
          style={{
            backgroundImage: `linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.6)), url('${heroData.background_image || 'https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape'}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundAttachment: 'scroll'
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative z-10 h-full flex flex-col justify-center items-center text-center px-6">
            {/* Logo Icon */}
            <motion.div
              className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-3"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.1, type: 'spring', stiffness: 200 }}
            >
              <i className="ri-home-4-fill text-2xl text-white"></i>
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-white mb-2 max-w-xs leading-tight"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {heroData.title}
            </motion.h1>

            <motion.p
              className="text-white/90 text-sm mb-6 leading-relaxed max-w-sm"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              {heroData.subtitle}
            </motion.p>

            <motion.div
              className="flex flex-col gap-3 w-full max-w-xs"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <motion.button
                onClick={openWhatsApp}
                className="bg-white text-orange-500 px-6 py-3 rounded-2xl font-bold text-base shadow-xl flex items-center justify-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="ri-whatsapp-line text-xl"></i>
                تواصل معنا الآن
              </motion.button>

              <motion.div
                className="flex items-center justify-center gap-4 text-white/80 text-xs"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <div className="flex items-center gap-1">
                  <i className="ri-phone-line text-sm"></i>
                  <span>اتصال مجاني</span>
                </div>
                <div className="w-1 h-1 bg-white/60 rounded-full"></div>
                <div className="flex items-center gap-1">
                  <i className="ri-time-line text-sm"></i>
                  <span>استشارة فورية</span>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.section>
      )}

      {/* Quick Actions */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mb-4">
              <i className="ri-restaurant-line text-2xl text-orange-500"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-800 mb-2">المطابخ</h3>
            <p className="text-gray-600 text-sm">تصاميم عصرية وعملية</p>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
              <i className="ri-archive-line text-2xl text-purple-500"></i>
            </div>
            <h3 className="text-lg font-bold text-gray-800 mb-2">الخزائن</h3>
            <p className="text-gray-600 text-sm">حلول تخزين ذكية</p>
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Kitchens */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800">مطابخ مميزة</h2>
          <motion.button
            className="text-orange-500 font-medium flex items-center gap-1"
            whileHover={{ scale: 1.05 }}
          >
            عرض الكل
            <i className="ri-arrow-left-line"></i>
          </motion.button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {kitchens.map((kitchen, index) => (
            <motion.div
              key={kitchen.id}
              className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setSelectedProduct(kitchen);
                setSelectedProductType('kitchen');
              }}
            >
              <div className="aspect-square relative overflow-hidden">
                <Image
                  src={kitchen.images && kitchen.images[0] ? kitchen.images[0].image_url : '/uploads/placeholder.jpg'}
                  alt={kitchen.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
                <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded-lg text-xs font-medium">
                  جديد
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-bold text-gray-800 mb-1 text-sm">{kitchen.title}</h3>
                <p className="text-gray-600 text-xs line-clamp-2">{kitchen.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Featured Cabinets */}
      <motion.section
        className="px-4 py-6"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800">خزائن مميزة</h2>
          <motion.button
            className="text-purple-500 font-medium flex items-center gap-1"
            whileHover={{ scale: 1.05 }}
          >
            عرض الكل
            <i className="ri-arrow-left-line"></i>
          </motion.button>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {cabinets.map((cabinet, index) => (
            <motion.div
              key={cabinet.id}
              className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 * index }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setSelectedProduct(cabinet);
                setSelectedProductType('cabinet');
              }}
            >
              <div className="aspect-square relative overflow-hidden">
                <Image
                  src={cabinet.images && cabinet.images[0] ? cabinet.images[0].image_url : '/uploads/placeholder.jpg'}
                  alt={cabinet.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
                <div className="absolute top-2 right-2 bg-purple-500 text-white px-2 py-1 rounded-lg text-xs font-medium">
                  مميز
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-bold text-gray-800 mb-1 text-sm">{cabinet.title}</h3>
                <p className="text-gray-600 text-xs line-clamp-2">{cabinet.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.section>

      {/* Contact CTA */}
      <motion.section
        className="px-4 py-6 pb-8"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.9 }}
      >
        <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-center">
          <h3 className="text-xl font-bold text-white mb-2">هل تحتاج استشارة؟</h3>
          <p className="text-white/90 mb-4">تواصل معنا للحصول على استشارة مجانية</p>
          <motion.button
            onClick={openWhatsApp}
            className="bg-white text-orange-500 px-6 py-3 rounded-full font-bold flex items-center gap-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-whatsapp-line text-xl"></i>
            تواصل عبر واتساب
          </motion.button>
        </div>
      </motion.section>

      {/* Product Modal */}
      <AnimatePresence>
        {selectedProduct && (
          <MobileProductModal
            product={selectedProduct}
            type={selectedProductType}
            onClose={() => {
              setSelectedProduct(null);
              setSelectedProductType(null);
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default MobileHome;
