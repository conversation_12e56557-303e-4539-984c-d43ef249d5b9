import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const MobileContact = () => {
  const [socialMedia, setSocialMedia] = useState([]);

  useEffect(() => {
    fetchSocialMedia();
  }, []);

  const fetchSocialMedia = async () => {
    try {
      const response = await fetch('/api/footer');
      const data = await response.json();
      setSocialMedia(data.socialMedia || []);
    } catch (error) {
      console.error('Error fetching social media:', error);
    }
  };



  const openWhatsApp = () => {
    const message = "مرحباً، أريد الاستفسار عن خدماتكم";
    const phoneNumber = "966557611105";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  const contactMethods = [
    {
      icon: 'ri-whatsapp-line',
      title: 'واتساب',
      value: '+966 55 761 1105',
      action: openWhatsApp,
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-phone-line',
      title: 'اتصال مباشر',
      value: '+966 55 761 1105',
      action: () => window.open('tel:+966557611105'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-mail-line',
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>'),
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: 'ri-map-pin-line',
      title: 'الموقع',
      value: 'الدمام، شارع الملك عبدالعزيز',
      action: () => window.open('https://www.google.com/maps/place/26%C2%B027\'07.4%22N+50%C2%B004\'18.9%22E/@26.4517325,50.072148,19.25z/data=!4m4!3m3!8m2!3d26.452062!4d50.0719285!5m2!1e4!1e2?hl=ar&entry=ttu&g_ep=EgoyMDI1MDcxMy4wIKXMDSoASAFQAw%3D%3D'),
      color: 'from-red-500 to-red-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <motion.section
        className="bg-gradient-to-br from-green-500 to-teal-500 p-6 text-center text-white"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring' }}
        >
          <i className="ri-customer-service-2-fill text-3xl text-white"></i>
        </motion.div>
        <motion.h1
          className="text-3xl font-bold mb-3"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          تواصل معنا
        </motion.h1>
        <motion.p
          className="text-lg opacity-90"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          نحن هنا لخدمتك في أي وقت
        </motion.p>
      </motion.section>

      {/* Quick Contact Methods */}
      <motion.section
        className="p-4 -mt-8 relative z-10"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div className="space-y-3">
          {contactMethods.map((method, index) => (
            <motion.button
              key={index}
              onClick={method.action}
              className={`w-full bg-gradient-to-r ${method.color} rounded-2xl p-4 text-white shadow-lg`}
              initial={{ x: -50, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6 + index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                  <i className={`${method.icon} text-2xl`}></i>
                </div>
                <div className="flex-1 text-right">
                  <h3 className="font-bold text-lg">{method.title}</h3>
                  <p className="opacity-90 text-sm" style={{ direction: 'ltr', textAlign: 'right' }}>{method.value}</p>
                </div>
                <i className="ri-arrow-left-line text-xl opacity-70"></i>
              </div>
            </motion.button>
          ))}
        </div>
      </motion.section>



      {/* Google Map */}
      <motion.section
        className="p-4"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.0 }}
      >
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-xl font-bold text-gray-800 mb-4 text-right">موقعنا على الخريطة</h3>
          <div className="relative h-64 rounded-xl overflow-hidden">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3576.8234567890123!2d50.0719285!3d26.452062!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzA3LjQiTiA1MMKwMDQnMTguOSJF!5e0!3m2!1sar!2ssa!4v1234567890123!5m2!1sar!2ssa"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="موقع عجائب الخبراء"
            ></iframe>
          </div>
          <div className="mt-4 text-center">
            <p className="text-gray-600 text-sm mb-2">المملكة العربية السعودية</p>
            <p className="text-gray-800 font-medium">الدمام - شارع الملك عبدالعزيز</p>
          </div>
        </div>
      </motion.section>

      {/* Social Media */}
      {socialMedia.length > 0 && (
        <motion.section
          className="p-4"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.2 }}
        >
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-right">تابعنا على</h3>
            <div className="flex justify-center gap-3 flex-wrap">
              {socialMedia.map((social, index) => {
                const getBackgroundClass = (platform) => {
                  const platformLower = platform.toLowerCase();
                  if (platformLower.includes('whatsapp')) return 'bg-green-500';
                  if (platformLower.includes('instagram')) return 'bg-gradient-to-r from-purple-500 to-pink-500';
                  if (platformLower.includes('twitter')) return 'bg-black';
                  if (platformLower.includes('snapchat')) return 'bg-yellow-500';
                  if (platformLower.includes('tiktok')) return 'bg-black';
                  return 'bg-gray-600';
                };

                return (
                  <motion.a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center justify-center w-12 h-12 text-white rounded-full shadow-lg ${getBackgroundClass(social.platform)}`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 1.3 + index * 0.1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <i className={social.icon + " text-lg"}></i>
                  </motion.a>
                );
              })}
            </div>
          </div>
        </motion.section>
      )}

      {/* Working Hours */}
      <motion.section
        className="p-4 pb-8"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.4 }}
      >
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 text-white">
          <h3 className="text-xl font-bold mb-4 text-right">ساعات العمل</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="opacity-90">9:00 ص - 6:00 م</span>
              <span>السبت - الخميس</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="opacity-90">مغلق</span>
              <span>الجمعة</span>
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
};

export default MobileContact;
