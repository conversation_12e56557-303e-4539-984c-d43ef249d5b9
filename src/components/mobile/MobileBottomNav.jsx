import React from 'react';
import { motion } from 'framer-motion';

const MobileBottomNav = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'home',
      label: 'الرئيسية',
      icon: 'ri-home-4-line',
      activeIcon: 'ri-home-4-fill',
      color: 'text-orange-500'
    },
    {
      id: 'kitchens',
      label: 'المطابخ',
      icon: 'ri-restaurant-line',
      activeIcon: 'ri-restaurant-fill',
      color: 'text-orange-500'
    },
    {
      id: 'cabinets',
      label: 'الخزائن',
      icon: 'ri-archive-line',
      activeIcon: 'ri-archive-fill',
      color: 'text-purple-500'
    },
    {
      id: 'about',
      label: 'من نحن',
      icon: 'ri-information-line',
      activeIcon: 'ri-information-fill',
      color: 'text-blue-500'
    },
    {
      id: 'contact',
      label: 'تواصل معنا',
      icon: 'ri-phone-line',
      activeIcon: 'ri-phone-fill',
      color: 'text-green-500'
    }
  ];

  return (
    <motion.nav 
      className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-bottom">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id;
          
          return (
            <motion.button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-200 min-w-0 flex-1 ${
                isActive 
                  ? 'bg-gray-50' 
                  : 'hover:bg-gray-50'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Icon */}
              <motion.div
                className={`flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 ${
                  isActive 
                    ? `${tab.color} bg-current bg-opacity-10` 
                    : 'text-gray-400'
                }`}
                animate={{
                  scale: isActive ? 1.1 : 1,
                }}
              >
                <i className={`${isActive ? tab.activeIcon : tab.icon} text-xl`}></i>
              </motion.div>
              
              {/* Label */}
              <motion.span
                className={`text-xs font-medium mt-1 transition-all duration-200 truncate ${
                  isActive 
                    ? tab.color 
                    : 'text-gray-400'
                }`}
                animate={{
                  scale: isActive ? 1.05 : 1,
                }}
              >
                {tab.label}
              </motion.span>
              
              {/* Active Indicator */}
              {isActive && (
                <motion.div
                  className={`w-1 h-1 rounded-full mt-1 ${tab.color.replace('text-', 'bg-')}`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                />
              )}
            </motion.button>
          );
        })}
      </div>
    </motion.nav>
  );
};

export default MobileBottomNav;
