// API Configuration
// إعدادات API للبيئات المختلفة

const getApiConfig = () => {
  // تحديد URL الصحيح للـ API server
  // في الإنتاج نستخدم localhost:3003 للـ API server
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (typeof window === 'undefined') {
    // Server-side: استخدم localhost مباشرة
    return {
      baseURL: 'http://localhost:3003',
      uploadsURL: '/uploads'
    };
  } else {
    // Client-side: استخدم نفس الدومين للـ API routes
    // nginx سيوجه /api إلى localhost:3003
    return {
      baseURL: '',
      uploadsURL: '/uploads'
    };
  }
};

export const API_CONFIG = getApiConfig();

// دالة مساعدة لبناء URL كامل للصور
export const getImageURL = (imagePath) => {
  if (!imagePath) return null;

  // إذا كان الرابط كاملاً بالفعل، أرجعه كما هو
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // الصور الآن في public/uploads، لذا نستخدم روابط نسبية مباشرة
  // هذا يعمل مع HTTP و HTTPS تلقائياً
  return imagePath;
};

// دالة مساعدة لبناء URL للـ API
export const getApiURL = (endpoint) => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};
