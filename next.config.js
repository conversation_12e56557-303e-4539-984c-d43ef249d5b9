/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode
  reactStrictMode: true,

  // Configure images
  images: {
    domains: ['images.unsplash.com', 'localhost', 'readdy.ai'],
    unoptimized: false, // Enable optimization for better performance
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Configure for server deployment
  trailingSlash: false,

  // Configure asset prefix for production
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',

  // Configure base path if needed
  basePath: '',

  // Disable x-powered-by header
  poweredByHeader: false,

  // Experimental features for better performance
  experimental: {
    optimizeCss: false, // Disable to fix critters error
    scrollRestoration: true,
  },

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // Configure webpack for sql.js
  webpack: (config, { isServer }) => {
    // Handle sql.js
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };

    // Handle .wasm files for sql.js
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };

    return config;
  },
  

};

module.exports = nextConfig;
